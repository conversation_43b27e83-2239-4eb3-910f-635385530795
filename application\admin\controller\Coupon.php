<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\exception\DbException;

/**
 * 优惠券管理
 *
 * @icon fa fa-circle-o
 */
class Coupon extends Backend
{

    /**
     * Coupon模型对象
     * @var \app\admin\model\Coupon
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Coupon;
        $this->view->assign("statusList", $this->model->getStatusList());
    }


    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    public function add()
    {
        if ($this->request->isPost() == true) {
            $params = $this->request->param('row/a');
            if ($params['start'] <= $params['price']) $this->error('抵扣金额不能大于或等于使用门槛！');
        }
        return parent::add();
    }

    public function edit($ids = null)
    {
        if ($this->request->isPost() == true) {
            $params = $this->request->param('row/a');
            if ($params['start'] <= $params['price']) $this->error('抵扣金额不能大于或等于使用门槛！');
        }
        return parent::edit($ids);
    }


    /**
     * 发送优惠券
     * @return string|void
     * @throws DbException
     * @throws \think\Exception
     */
    public function send_coupon()
    {
        $couponId    = input('couponId');
        $userIds     = input('userIds/a');
        $couponModel = new \app\common\model\Coupon();
        $result      = $couponModel->sendCoupon($couponId, $userIds);
        if (!$result) {
            $this->error($couponModel->getError());
        }
        $this->success('操作成功');
    }


}
