<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\AgentInviteLog;
use think\Db;
/**
 * 技师管理
 *
 * @icon fa fa-circle-o
 */
class Technicians extends Backend
{

    /**
     * Technicians模型对象
     * @var \app\admin\model\Technicians
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Technicians;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with(['user','shop','area'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->getRelation('user')->visible(['nickname']);
                $row->getRelation('shop')->visible(['name']);
                $row->agent_name = '';
                $inviteLog = Db::name('agent_invite_log')->where(['identity'=>'TECHNICIANS','link_id'=>$row->id])->find();
                if($inviteLog){
                    $agentInfo = Db::name('agent')->where('id',$inviteLog['agent_id'])->find();
                    $row->agent_name = $agentInfo['real_name']??'';
                }
            }
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     * @param $ids
     * @return string
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost() === false) {
            $agentRealName = '暂无';
            $agentLog      = AgentInviteLog::get(['link_id' => $ids,'identity'=>'TECHNICIANS']);
            if ($agentLog) {
                $agentInfo = \app\common\model\Agent::get($agentLog->agent_id);
                if ($agentInfo) {
                    $agentRealName = $agentInfo->real_name ?? $agentRealName;
                }
            }
            $row->agent_real_name = $agentRealName;
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        return parent::edit($ids);
    }

}
