<?php

namespace app\admin\controller\user;

use addons\epay\library\Service;
use app\common\controller\Backend;
use app\common\model\UserWithdrawAccount;
use fast\WechatWithDraw;
use think\Db;
use think\Exception;
use Yansongda\Pay\Pay;

/**
 * 提现管理
 *
 * @icon fa fa-circle-o
 */
class Withdraw extends Backend
{

    /**
     * Withdraw模型对象
     * @var \app\admin\model\user\Withdraw
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\user\Withdraw;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign('typeList', (new UserWithdrawAccount)->getWithdrawAccountType());
    }

    /**
     * 查看
     */
    public function index()
    {
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->with(['user'])
                ->where($where)
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->with(['user', 'technicians', 'shop', 'agent', 'store'])
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

            $list   = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 提现详情
     * @param $ids
     * @return string
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function edit($ids = null)
    {
        $row = Db::name('withdraw')->where('id', $ids)->find();
        if (!$row) {
            $this->error('未找到记录');
        }
        $row['nickname'] = \app\common\model\User::get($row['user_id'])->value('nickname');
        ksort($row);
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }

    //查询转账状态
    public function query()
    {
        $ids   = $this->request->param('ids', '');
        $model = $this->model->where('id', $ids)->find();
        if (!$model) {
            $this->error(__('No Results were found'));
        }
        $info = get_addon_info('epay');
        if (!$info || !$info['state']) {
            $this->error('请检查微信支付宝整合插件是否正确安装且启用');
        }
        $result = null;
        try {
            $config = Service::getConfig('alipay');
            $pay    = Pay::alipay($config);
            $result = $pay->find($model['orderid'], 'transfer');

        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        if ($result && isset($result['code']) && $result['code'] == 10000) {
            $this->success("转账成功！");
        } else {
            $this->error('转账失败！');
        }
    }

    /**
     * 审核通过
     * @param $ids
     * @return void
     * @throws \think\exception\DbException
     */
    public function check($ids = null)
    {
        $withdrawModel = new \app\common\model\Withdraw();
        $result        = $withdrawModel->check($ids);
        if (!$result) {
            $this->error($withdrawModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 审核驳回
     * @param $ids
     * @return void
     * @throws \think\exception\DbException
     */
    public function close($ids = null)
    {
        $withdrawModel = new \app\common\model\Withdraw();
        $result        = $withdrawModel->check($ids, 'rejected');
        if (!$result) {
            $this->error($withdrawModel->getError());
        }
        $this->success('操作成功');
    }
}
