<?php

return [
    'Store_id'                => '渠道商id',
    'Agent_id'                => '代理商id',
    'Shop_id'                 => '门店id',
    'OrderId'                 => '订单编号',
    'User_id'                 => '用户id',
    'User_json'               => '用户信息',
    'Technicians_id'          => '技师id',
    'Technicians_json'        => '技师信息',
    'Kind'                    => '类型',
    'Kind DOOR'               => '上门服务',
    'Kind SHOP'               => '到店服务',
    'Address_id'              => '地址id',
    'Address_json'            => '地址详情',
    'Server_time'             => '服务时间',
    'Server_id'               => '服务项目id',
    'Server_json'             => '服务项目详情',
    'Technicians_server_id'   => '技师服务项目id',
    'Technicians_server_json' => '技师服务详情',
    'Server_price'            => '套餐总价',
    'Travel_price'            => '路费',
    'Store_price'             => '渠道商分佣',
    'Technicians_price'       => '技师分佣',
    'Agent_price'             => '代理商分佣',
    'Shop_price'              => '门店分佣',
    'Reward_price'            => '打赏金额',
    'System_price'            => '平台分佣',
    'Refund_price'            => '退款金额',
    'User_coupon_log_id'      => '优惠券id',
    'User_coupon_json'        => '优惠券详情',
    'Coupon_price'            => '优惠券抵扣金额',
    'Total_price'             => '应付金额',
    'Yue_price'               => '余额支付',
    'Wechat_price'            => '微信支付',
    'Pay_price'               => '实付金额',
    'Remark'                  => '备注',
    'Pay_type'                => '支付方式',
    'Status'                  => '状态',
    'Status 0'                => '待支付',
    'Set status to 0'         => '设为待支付',
    'Status 1'                => '待接单',
    'Set status to 1'         => '设为待接单',
    'Status 2'                => '待出发',
    'Set status to 2'         => '设为待出发',
    'Status 3'                => '待开始',
    'Set status to 3'         => '设为待开始',
    'Status 4'                => '服务中',
    'Set status to 4'         => '设为服务中',
    'Status 5'                => '待评价',
    'Set status to 5'         => '设为待评价',
    'Status 6'                => '已完成',
    'Set status to 6'         => '设为已完成',
    'Status 7'                => '已取消',
    'Set status to 7'         => '设为已取消',
    'Create_time'             => '下单时间',
    'Pay_time'                => '付款时间',
    'Get_order_time'          => '接单时间',
    'Set_out_order_time'      => '出发时间',
    'Start_order_time'        => '开始时间',
    'End_order_time'          => '结束时间',
    'Over_order_time'         => '完成时间',
    'Close_order_time'        => '取消时间',
    'Server_over_time'        => '预计服务结束时间',
    'Transaction_id'          => '微信订单流水号',
    'Close_remark'            => '取消原因',
    'Close_kind'              => '取消方USER用户取消TECHNICIANS技师取消',
    'P_order_id'              => '父订单ID',
    'Is_arrived'              => '技师已到达0否1是2用户确认',
    'Server_image'            => '技师打卡图片'
];
