<?php

namespace app\admin\model;

use app\common\model\AgentInviteLog;
use think\Model;


class Agent extends Model
{

    

    

    // 表名
    protected $name = 'agent';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];

    public function getOriginData()
    {
        return $this->origin;
    }

    public static function init()
    {
        self::afterWrite(function ($row) {
            $changedData = $row->getChangedData();
        });
        self::beforeUpdate(function ($row) {
            $changedata = $row->getChangedData();
            $origin     = $row->getOriginData();
            if (isset($changedata['money']) && (function_exists('bccomp') ? bccomp($changedata['money'], $origin['money'], 2) !== 0 : (double)$changedata['money'] !== (double)$origin['money'])) {
                \app\common\model\AgentMoneyLog::create(['agent_id' => $row['id'], 'money' => $changedata['money'] - $origin['money'], 'before' => $origin['money'], 'after' => $changedata['money'], 'memo' => '管理员变更金额', 'createtime' => time()]);
            }
        });
    }
    
    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    public function area()
    {
        return $this->belongsTo('AreaCode', 'area_code_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
