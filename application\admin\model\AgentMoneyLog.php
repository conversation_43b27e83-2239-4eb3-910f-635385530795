<?php

namespace app\admin\model;

use think\Model;


class AgentMoneyLog extends Model
{

    

    

    // 表名
    protected $name = 'agent_money_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kind_text'
    ];
    

    
    public function getKindList()
    {
        return ['GET' => __('收入'), 'WITHDRAW' => __('提现')];
    }


    public function getKindTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['kind']) ? $data['kind'] : '');
        $list = $this->getKindList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function agent()
    {
        return $this->belongsTo('Agent', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
