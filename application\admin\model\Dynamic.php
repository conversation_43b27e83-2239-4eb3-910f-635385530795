<?php

namespace app\admin\model;

use think\Model;


class Dynamic extends Model
{

    

    

    // 表名
    protected $name = 'dynamic';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kind_text'
    ];
    

    
    public function getKindList()
    {
        return ['shop' => __('Shop'), 'technicians' => __('Technicians')];
    }


    public function getKindTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['kind']) ? $data['kind'] : '');
        $list = $this->getKindList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function technicians()
    {
        return $this->belongsTo('Technicians', 'technicians_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
