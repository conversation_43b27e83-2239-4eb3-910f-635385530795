<?php

namespace app\admin\model;

use think\Model;


class EvaluateSet extends Model
{

    

    

    // 表名
    protected $name = 'evaluate_set';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            if (!$row['weigh']) {
                $pk = $row->getPk();
                $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
            }
        });
    }


    public function getKindList()
    {
        return ['USER' => __('用户'), 'TECHNICIANS' => __('技师')];
    }







}
