<?php

namespace app\admin\model;

use app\manystore\model\Manystore;
use app\manystore\model\ManystoreAuthGroup;
use app\manystore\model\ManystoreAuthGroupAccess;
use think\Model;


class Franchisees extends Model
{


    // 表名
    protected $name = 'franchisees';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];

    public function getOriginData()
    {
        return $this->origin;
    }

    public static function init()
    {
        self::afterWrite(function ($row) {
            $changedData = $row->getChangedData();
            if (isset($changedData['address']) || isset($changedData['info'])) {
                self::where(['id' => $row->id])->update(['address_info' => $row->address . $row->info]);
            }
        });
        self::afterUpdate(function ($row) {
            #修改商家手机号
            $changedData   = $changedata = $row->getChangedData();
            $manystoreInfo = Manystore::get(['shop_id' => $row->id]);
            if (isset($changedData['mobile']) && $manystoreInfo) {
                $manystoreInfo->save(['mobile' => $row->mobile]);
            }
            if ($row->status == 1) {
                #审核通过创建商家账号
                $userInfo = \app\common\model\User::get($row->user_id);
                if ($userInfo) {
                    if ($manystoreInfo) {
                        $manystoreInfo->save(['status' => 'normal']);
                    } else {
                        $manystore                     = [
                            'shop_id'    => $row->id,
                            'is_main'    => 1,
                            'username'   => $userInfo->username,
                            'nickname'   => $row['real_name'],
                            'password'   => $userInfo->password,
                            'salt'       => $userInfo->salt,
                            'avatar'     => $userInfo->avatar,
                            'email'      => $userInfo->email,
                            'createtime' => time(),
                            'mobile'     => $row->mobile
                        ];
                        $manystore_id                  = (new \app\manystore\model\Manystore)->insertGetId($manystore);
                        $manystoreAuthGroupModel       = new ManystoreAuthGroup();
                        $group                         = [];
                        $group['shop_id']              = $row->id;
                        $group['name']                 = '超级管理员';
                        $group['rules']                = '*';
                        $group['createtime']           = time();
                        $group['updatetime']           = time();
                        $group_id                      = $manystoreAuthGroupModel->insertGetId($group);
                        $manystoreAuthGroupAccessModel = new ManystoreAuthGroupAccess();
                        $group_access                  = [];
                        $group_access['uid']           = $manystore_id;
                        $group_access['group_id']      = $group_id;
                        $manystoreAuthGroupAccessModel->insert($group_access);
                    }
                }
            } else {
                #审核拒绝，存在则隐藏商家账号
                if ($manystoreInfo) {
                    $manystoreInfo->save(['status' => 'hidden']);
                }
            }
            $origin = $row->getOriginData();
            if (isset($changedata['money']) && (function_exists('bccomp') ? bccomp($changedata['money'], $origin['money'], 2) !== 0 : (double)$changedata['money'] !== (double)$origin['money'])) {
                \app\common\model\FranchiseesMoneyLog::create(['franchisees_id' => $row['id'], 'money' => $changedata['money'] - $origin['money'], 'before' => $origin['money'], 'after' => $changedata['money'], 'memo' => '管理员变更金额', 'createtime' => time()]);
            }
        });
    }

    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list  = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
      public function area()
    {
        return $this->belongsTo('AreaCode', 'area_code_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
