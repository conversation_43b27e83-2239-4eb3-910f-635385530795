<?php

namespace app\admin\model;

use app\common\model\TechniciansNotice;
use app\common\model\UserNotice;
use app\job\AdminNotice;
use think\Model;
use think\Queue;


class Notice extends Model
{


    // 表名
    protected $name = 'notice';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];

    public static function init()
    {
        self::afterInsert(function ($row) {
            $row = $row->toArray();
            #消息通知队列
            Queue::push(AdminNotice::class, $row);
        });
    }


}
