<?php

namespace app\admin\model;

use think\Model;


class Order extends Model
{

    

    

    // 表名
    protected $name = 'order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kind_text',
        'server_time_text',
        'pay_type_text',
        'status_text',
        'create_time_text',
        'pay_time_text',
        'get_order_time_text',
        'set_out_order_time_text',
        'start_order_time_text',
        'end_order_time_text',
        'over_order_time_text',
        'close_order_time_text',
        'server_over_time_text',
        'close_kind_text'
    ];
    

    
    public function getKindList()
    {
        return ['DOOR' => __('Kind DOOR'), 'SHOP' => __('Kind SHOP')];
    }

    public function getPayTypeList()
    {
        return ['WECHAT' => __('WECHAT'), 'YUE' => __('YUE')];
    }

    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3'), '4' => __('Status 4'), '5' => __('Status 5'), '6' => __('Status 6'), '7' => __('Status 7')];
    }

    public function getCloseKindList()
    {
        return ['USER' => __('USER'), 'TECHNICIANS' => __('TECHNICIANS')];
    }


    public function getKindTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['kind']) ? $data['kind'] : '');
        $list = $this->getKindList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getServerTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['server_time']) ? $data['server_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getPayTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_type']) ? $data['pay_type'] : '');
        $list = $this->getPayTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['create_time']) ? $data['create_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getPayTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_time']) ? $data['pay_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getGetOrderTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['get_order_time']) ? $data['get_order_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getSetOutOrderTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['set_out_order_time']) ? $data['set_out_order_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStartOrderTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['start_order_time']) ? $data['start_order_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getEndOrderTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['end_order_time']) ? $data['end_order_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getOverOrderTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['over_order_time']) ? $data['over_order_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getCloseOrderTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['close_order_time']) ? $data['close_order_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getServerOverTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['server_over_time']) ? $data['server_over_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getCloseKindTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['close_kind']) ? $data['close_kind'] : '');
        $list = $this->getCloseKindList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setServerTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setCreateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setPayTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setGetOrderTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setSetOutOrderTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setStartOrderTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setEndOrderTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setOverOrderTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setCloseOrderTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setServerOverTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


}
