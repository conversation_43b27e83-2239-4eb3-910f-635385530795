<?php

namespace app\admin\model;

use think\Model;


class Reward extends Model
{

    

    

    // 表名
    protected $name = 'reward';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];


    public function getStatusList()
    {
        return ['0' => __('未支付'), '1' => __('已支付')];
    }




    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function technicians()
    {
        return $this->belongsTo('Technicians', 'technicians_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
