<?php

namespace app\admin\model;

use think\Model;


class StoreMoneyLog extends Model
{

    

    

    // 表名
    protected $name = 'store_money_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kind_text'
    ];
    

    
    public function getKindList()
    {
        return ['GET' => __('GET'), 'WITHDRAW' => __('WITHDRAW')];
    }


    public function getKindTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['kind']) ? $data['kind'] : '');
        $list = $this->getKindList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function store()
    {
        return $this->belongsTo('Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
