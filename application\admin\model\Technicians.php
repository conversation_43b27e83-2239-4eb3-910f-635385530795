<?php

namespace app\admin\model;

use app\common\model\AgentInviteLog;
use think\Model;


class Technicians extends Model
{


    // 表名
    protected $name = 'technicians';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];

    public function getOriginData()
    {
        return $this->origin;
    }

    public static function init()
    {
        self::afterWrite(function ($row) {
            $changedData = $row->getChangedData();
            /*if (isset($changedData['status'])) {
                if ($changedData['status'] == '1') {
                    User::where('id', '=', $row['user_id'])->update(['identity' => 'TECHNICIAN']);
                } else {
                    User::where('id', '=', $row['user_id'])->update(['identity' => 'USER']);
                }
            }*/
        });
        self::afterWrite(function ($row) {
            if ($row['status'] == '1') {
                #技师审核通过后绑定技师邀请关系
                (new AgentInviteLog())->saveAgentInviteLog($row->id);
                #自动生成一条代理信息
                $same = (new \app\common\model\Agent())->where(['user_id' => $row->user_id])->find();
                $data = [
                    'user_id'          => $row->user_id,
                    'real_name'        => $row->real_name,
                    'mobile'           => $row->mobile,
                    'status'           => '1',
                    'create_time'      => datetime(time()),
                    'kind'             => 'TECHNICIANS',
                    'technicians_id'   => $row->id,
                    'id_card_front'    => $row->id_card_front,
                    'id_card_opposite' => $row->id_card_opposite,
                    'franchisees_id'   => $row->franchisees_id,
                    'area_code_id'     => $row->area_code_id??''
                    ];
                if (!$same) {
                    (new \app\common\model\Agent())->create($data);
                } else {
                    $same->save($data);
                }
            }
        });
        self::beforeUpdate(function ($row) {
            $changedata = $row->getChangedData();
            $origin     = $row->getOriginData();
            if (isset($changedata['money']) && (function_exists('bccomp') ? bccomp($changedata['money'], $origin['money'], 2) !== 0 : (double)$changedata['money'] !== (double)$origin['money'])) {
                \app\common\model\TechniciansMoneyLog::create(['technicians_id' => $row['id'], 'money' => $changedata['money'] - $origin['money'], 'before' => $origin['money'], 'after' => $changedata['money'], 'memo' => '管理员变更金额', 'createtime' => time()]);
            }
        });
    }


    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list  = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
     public function shop()
    {
        return $this->belongsTo('Shop', 'shop_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
      public function area()
    {
        return $this->belongsTo('AreaCode', 'area_code_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
