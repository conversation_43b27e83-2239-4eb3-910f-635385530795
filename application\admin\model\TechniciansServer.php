<?php

namespace app\admin\model;

use think\Model;


class TechniciansServer extends Model
{

    

    

    // 表名
    protected $name = 'technicians_server';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];


    public function set()
    {
        return $this->belongsTo('KilometersSet', 'kilometers_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function currency()
    {
        return $this->belongsTo('Currency', 'currency_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function server()
    {
        return $this->belongsTo('Server', 'server_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function technicians()
    {
        return $this->belongsTo('Technicians', 'technicians_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

}
