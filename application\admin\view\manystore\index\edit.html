<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="row">
        <div class="col-md-12 col-sm-12">
            <div class="panel panel-default panel-intro">
                <div class="panel-heading">
                    <ul class="nav nav-tabs">
                        <li class="active"><a href="#basic" data-toggle="tab">基础信息</a></li>
                    </ul>
                </div>
                <div class="panel-body">
                    <div id="myTabContent" class="tab-content">
                        <div class="tab-pane fade active in" id="basic">
                            <div class="form-group">
                                <label  class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
                                <div class="col-xs-12 col-sm-8" style="margin-top:7px;">
                                    {$row.username|htmlentities}
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="email" class="control-label col-xs-12 col-sm-2">{:__('Email')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input type="email" class="form-control" id="email" name="row[email]" value="{$row.email|htmlentities}" data-rule="required;email" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input type="text" class="form-control" id="nickname" name="row[nickname]" autocomplete="off" value="{$row.nickname|htmlentities}" data-rule="required" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input type="password" class="form-control" id="password" name="row[password]" autocomplete="new-password" value="" data-rule="password" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="loginfailure" class="control-label col-xs-12 col-sm-2">{:__('Loginfailure')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input type="number" class="form-control" id="loginfailure" name="row[loginfailure]" value="{$row.loginfailure}" data-rule="required" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="col-md-12 col-sm-12">
            <div class="panel panel-default panel-intro">
                <div class="panel-heading">
                    <ul class="nav nav-tabs">
                        <li class="active"><a href="#basic" data-toggle="tab">商家信息</a></li>
                    </ul>
                </div>
                <div class="panel-body">
                    <div id="storeContent" class="tab-content">
                        <div class="tab-pane fade active in" id="shop_basic">

                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input id="c-name" data-rule="required" class="form-control" name="shop[name]" type="text" value="{$shop.name}" >
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">
                                    {:__('Logo')}:
                                    <p style="margin-top: 20px;">建议40*40</p>
                                </label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="input-group">
                                        <input id="c-logo"  class="form-control" size="50" name="shop[logo]" type="text" value="{$shop.logo}" >
                                        <div class="input-group-addon no-border no-padding">
                                            <span><button type="button" id="plupload-logo" class="btn btn-danger plupload cropper" data-input-id="c-logo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-logo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-logo" class="btn btn-primary fachoose" data-input-id="c-logo" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                        </div>
                                        <span class="msg-box n-right" for="c-logo"></span>
                                    </div>
                                    <ul class="row list-inline plupload-preview" id="p-logo"></ul>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="input-group">
                                        <input id="c-image" data-rule="required" class="form-control" size="50" name="shop[image]" type="text" value="{$shop.image}" >
                                        <div class="input-group-addon no-border no-padding">
                                            <span><button type="button" id="plupload-image" class="btn btn-danger plupload cropper" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                        </div>
                                        <span class="msg-box n-right" for="c-image"></span>
                                    </div>
                                    <ul class="row list-inline plupload-preview" id="p-image"></ul>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Images')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="input-group">
                                        <input id="c-images"  class="form-control"  name="shop[images]" type="text" value="{$shop.images}">
                                        <div class="input-group-addon no-border no-padding">
                                            <span><button type="button" id="plupload-images" class="btn btn-danger plupload cropper" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="true" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-imagess" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                        </div>
                                        <span class="msg-box n-right" for="c-images"></span>
                                    </div>
                                    <ul class="row list-inline plupload-preview" id="p-images"></ul>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Address_city')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class='control-relative'>
                                        <input id="c-address_city" data-rule="required" class="form-control form-control" data-toggle="city-picker" name="shop[address_city]" value="{$shop.address_city}" type="text">
                                    </div>
                                    <input type="hidden" id="province" name="shop[province]" value="" >
                                    <input type="hidden" id="city" name="shop[city]" value="" >
                                    <input type="hidden" id="district" name="shop[district]" value="" >
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class='control-relative'>
                                        <input id="c-address" data-rule="required" class="form-control form-control"
                                               data-lat-id="c-latitude" data-lng-id="c-longitude" readonly data-input-id="c-address" data-toggle="addresspicker" name="shop[address]" value="{$shop.address}" type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Address_detail')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input id="c-address_detail" class="form-control" name="shop[address_detail]" type="text" value="{$shop.address_detail}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Longitude')}:</label>
                                <div class="col-xs-12 col-sm-3">
                                    <input id="c-longitude" data-rule="required" readonly class="form-control" name="shop[longitude]" type="text" value="{$shop.longitude}">
                                </div>
                                <label class="control-label col-xs-12 col-sm-2">{:__('Latitude')}:</label>
                                <div class="col-xs-12 col-sm-3">
                                    <input id="c-latitude" data-rule="required" readonly class="form-control" name="shop[latitude]" type="text" value="{$shop.latitude}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Yyzzdm')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input id="c-yyzzdm" data-rule="required" class="form-control" name="shop[yyzzdm]" type="text" value="{$shop.yyzzdm}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Yyzz_images')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="input-group">
                                        <input id="c-yyzz_images" data-rule="required" class="form-control" size="50" name="shop[yyzz_images]" type="text" value="{$shop.yyzz_images}">
                                        <div class="input-group-addon no-border no-padding">
                                            <span><button type="button" id="plupload-yyzz_images" class="btn btn-danger plupload" data-input-id="c-yyzz_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="true" data-preview-id="p-yyzz_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-yyzz_images" class="btn btn-primary fachoose" data-input-id="c-yyzz_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                        </div>
                                        <span class="msg-box n-right" for="c-yyzz_images"></span>
                                    </div>
                                    <ul class="row list-inline plupload-preview" id="p-yyzz_images"></ul>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Tel')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input id="c-tel" class="form-control" name="shop[tel]" type="text" value="{$shop.tel}" >
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <textarea id="c-content" class="form-control editor" rows="5" name="shop[content]" cols="50"  >{$shop.content}</textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    {:build_radios('shop[status]', ['0'=>__('Status 0'), '1'=>__('Status 1'), '2'=>__('Status 2')], $shop['status'])}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group hidden layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
