<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class=" control-label col-xs-12 col-sm-2">{:__('接收用户')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label for="row[is_all]-1">
                    <input type="radio" name="row[is_all]" id="row[is_all]-1" disabled {if $row.is_all== 1}checked{/if}
                    value="1">全员接收
                </label>
                <label for="row[is_all]-2">
                    <input type="radio" name="row[is_all]" id="row[is_all]-2" disabled {if $row.is_all== 2}checked{/if}
                    value="2">用户接收
                </label>
                <label for="row[is_all]-3">
                    <input type="radio" name="row[is_all]" id="row[is_all]-3" disabled {if $row.is_all== 3}checked{/if}
                    value="3">技师接收
                </label>
                <label for="row[is_all]-4">
                    <input type="radio" name="row[is_all]" id="row[is_all]-4" disabled {if $row.is_all== 4}checked{/if}
                    value="4">指定用户接收
                </label>
                <label for="row[is_all]-5">
                    <input type="radio" name="row[is_all]" id="row[is_all]-5" disabled {if $row.is_all== 5}checked{/if}
                    value="5">指定技师接收
                </label>
            </div>
        </div>
    </div>
    <div class="form-group" data-favisible="is_all=4">
        <label class="control-label col-xs-12 col-sm-2">{:__('用户昵称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_ids" data-source="user/user/index" data-field="nickname" data-multiple="true"
                   class="form-control selectpage" name="row[user_ids]" type="text" value="{$row.user_ids}">
        </div>
    </div>
    <div class="form-group" data-favisible="is_all=5">
        <label class="control-label col-xs-12 col-sm-2">{:__('技师昵称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-technicians_ids" data-source="technicians/index" data-field="nickname" data-multiple="true"
                   class="form-control selectpage" name="row[technicians_ids]" type="text"
                   value="{$row.technicians_ids}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control " rows="5" name="row[content]" cols="50">{$row.content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss"
                   data-use-current="true" name="row[create_time]" type="text" value="{$row.create_time}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
