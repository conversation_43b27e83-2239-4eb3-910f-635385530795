<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Shop_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-shop_id" data-rule="required" data-source="shop/index" class="form-control selectpage" name="row[shop_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('OrderId')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-orderId" class="form-control" name="row[orderId]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[user_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[user_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Technicians_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-technicians_id" data-rule="required" data-source="technicians/index" class="form-control selectpage" name="row[technicians_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Technicians_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[technicians_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[technicians_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Kind')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-kind" data-rule="required" class="form-control selectpicker" name="row[kind]">
                {foreach name="kindList" item="vo"}
                    <option value="{$key}" {in name="key" value="DOOR"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address_id" data-rule="required" data-source="address/index" class="form-control selectpage" name="row[address_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[address_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[address_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Server_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-server_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[server_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Server_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-server_id" data-rule="required" data-source="server/index" class="form-control selectpage" name="row[server_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Server_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[server_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[server_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Technicians_server_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-technicians_server_id" data-rule="required" data-source="technicians/server/index" class="form-control selectpage" name="row[technicians_server_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Technicians_server_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[technicians_server_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[technicians_server_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Server_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-server_price" data-rule="required" class="form-control" step="0.01" name="row[server_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Travel_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-travel_price" data-rule="required" class="form-control" step="0.01" name="row[travel_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_price" data-rule="required" class="form-control" step="0.01" name="row[store_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Technicians_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-technicians_price" data-rule="required" class="form-control" step="0.01" name="row[technicians_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_price" data-rule="required" class="form-control" step="0.01" name="row[agent_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Shop_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-shop_price" data-rule="required" class="form-control" step="0.01" name="row[shop_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reward_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-reward_price" data-rule="required" class="form-control" step="0.01" name="row[reward_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('System_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-system_price" data-rule="required" class="form-control" step="0.01" name="row[system_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund_price" data-rule="required" class="form-control" step="0.01" name="row[refund_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_coupon_log_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_coupon_log_id" data-rule="required" data-source="user/coupon/log/index" class="form-control selectpage" name="row[user_coupon_log_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_coupon_json')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[user_coupon_json]">
                <dd>
                    <ins>{:__('Key')}</ins>
                    <ins>{:__('Value')}</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                <textarea name="row[user_coupon_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Coupon_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-coupon_price" data-rule="required" class="form-control" step="0.01" name="row[coupon_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_price" data-rule="required" class="form-control" step="0.01" name="row[total_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Yue_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-yue_price" data-rule="required" class="form-control" step="0.01" name="row[yue_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wechat_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wechat_price" data-rule="required" class="form-control" step="0.01" name="row[wechat_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_price" data-rule="required" class="form-control" step="0.01" name="row[pay_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control " rows="5" name="row[remark]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-pay_type" data-rule="required" class="form-control selectpicker" name="row[pay_type]">
                {foreach name="payTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="WECHAT"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="0"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[pay_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Get_order_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-get_order_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[get_order_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Set_out_order_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-set_out_order_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[set_out_order_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Start_order_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-start_order_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[start_order_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('End_order_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-end_order_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[end_order_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Over_order_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-over_order_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[over_order_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Close_order_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-close_order_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[close_order_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Server_over_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-server_over_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[server_over_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Transaction_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-transaction_id" data-rule="required" data-source="transaction/index" class="form-control selectpage" name="row[transaction_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Close_remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-close_remark" class="form-control " rows="5" name="row[close_remark]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Close_kind')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-close_kind" data-rule="required" class="form-control selectpicker" name="row[close_kind]">
                {foreach name="closeKindList" item="vo"}
                    <option value="{$key}" {in name="key" value="USER"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('P_order_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-p_order_id" data-rule="required" data-source="p/order/index" class="form-control selectpage" name="row[p_order_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_arrived')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-is_arrived" data-rule="required" class="form-control" name="row[is_arrived]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Server_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-server_image" class="form-control" size="50" name="row[server_image]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-server_image" class="btn btn-danger faupload" data-input-id="c-server_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-server_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-server_image" class="btn btn-primary fachoose" data-input-id="c-server_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-server_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-server_image"></ul>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
