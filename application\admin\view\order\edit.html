<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单详情</title>
    <style>
        /* 全局样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f7fa;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        .order-container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        /* 进度展示容器 */
        .progress-display {
            position: relative;
            width: 100px;
            height: 100px;
            margin: 20px auto; /* 水平居中 */
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 旋转的圆圈 */
        .spinner {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #28a745; /* 顶部颜色为绿色 */
            border-radius: 50%;
            animation: spin 1s infinite linear;
        }

        /* 旋转动画 */
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* 进度文字 */
        .progress-text {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            z-index: 1; /* 确保文字在圆圈之上 */
        }

        /* 已完成和已取消状态，去掉旋转动画 */
        .progress-display.completed .spinner,
        .progress-display.cancelled .spinner {
            border-top-color: rgba(0, 0, 0, 0.1);
            animation: none;
        }

        /* 已完成状态，圆圈颜色为绿色 */
        .progress-display.completed .spinner {
            border-color: #28a745;
        }

        /* 已取消状态，圆圈颜色为红色 */
        .progress-display.cancelled .spinner {
            border-color: #e74c3c;
        }

        /* 服务项目样式 */
        .service-section {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 20px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .order-info {
            margin-bottom: 15px;
        }

        .order-info span {
            font-weight: bold;
            margin-right: 5px;
        }

        .service-table {
            width: 100%;
            border-collapse: collapse;
        }

        .service-table th,
        .service-table td {
            padding: 12px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }

        .service-table thead th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .total-row {
            text-align: right;
            font-weight: bold;
        }

        .total-amount {
            color: #e74c3c;
        }

        /* 订单信息样式 */
        .order-info-section {
            margin-bottom: 20px;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
        }

        .info-table td {
            padding: 10px;
            border: 1px solid #e0e0e0;
        }

        .info-table td:first-child {
            font-weight: bold;
            width: 150px;
        }

        .status-label,
        .type-label,
        .gender-label {
            padding: 4px 8px;
            border-radius: 4px;
            color: #fff;
            font-size: 12px;
        }

        .pending {
            background-color: #f39c12;
        }

        .waiting {
            background-color: #3498db;
        }

        .departing {
            background-color: #1abc9c;
        }

        .starting {
            background-color: #9b59b6;
        }

        .in-service {
            background-color: #27ae60;
        }

        .to-review {
            background-color: #d35400;
        }

        .completed {
            background-color: #2ecc71;
        }

        .cancelled {
            background-color: #e74c3c;
        }

        .door-service {
            background-color: #3498db;
        }

        .shop-service {
            background-color: #f1c40f;
        }

        .male {
            background-color: #3498db;
        }

        .female {
            background-color: #e84393;
        }

        /* 地址信息样式 */
        .address-section {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
<div class="order-container">
    <!-- 订单进度条 -->
    <div class="progress-display">
        <div class="spinner"></div>
        <div class="progress-text">
            {switch name="row.status"}
            {case value="0"}待支付{/case}
            {case value="1"}待接单{/case}
            {case value="2"}待出发{/case}
            {case value="3"}待开始{/case}
            {case value="4"}服务中{/case}
            {case value="5"}待评价{/case}
            {case value="6"}已完成{/case}
            {case value="7"}已取消{/case}
            {/switch}
        </div>
    </div>
    <!-- 服务项目 -->
    <div class="service-section">
        <h2 class="section-title">服务项目</h2>
        <p class="order-info">
            <span>订单号：</span><small>{$row.orderId}</small>
            <span style="margin-left: 15px">下单用户：</span><small>{$row.user_json.nickname}</small>
            <span style="margin-left: 15px">接单技师：</span><small>{$row.technicians_json.nickname}</small>
            <span style="margin-left: 15px">隶属门店：</span><small>{$row.shop_json.name??'无'}</small>
        </p>
        <table class="service-table">
            <thead>
            <tr>
                <th>服务图片</th>
                <th>服务名称</th>
                <th>套餐原价</th>
                <th>套餐总价</th>
                <th>往返路费</th>
                <th>优惠券金额</th>
                <th>合计</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td><img class="img-sm img-center" data-tips-image src="{$row.server_json.image|cdnurl|htmlentities}" alt="服务图片"></td>
                <td><strong>{$row.server_json.name}</strong></td>
                <td>￥{$row.old_server_price}</td>
                <td>￥{$row.travel_price}</td>
                <td>￥{$row.travel_price}</td>
                <td>￥{$row.coupon_price}</td>
                <td>￥{$row.total_price}</td>
            </tr>
            </tbody>
            <tfoot>
            <tr>
                <td colspan="7" class="total-row">
                    <span>支付金额：</span><span class="total-amount">￥{$row.pay_price}</span>
                </td>
            </tr>
            </tfoot>
        </table>
    </div>

    <!-- 订单信息 -->
    <div class="order-info-section">
        <h2 class="section-title">订单信息</h2>
        <table class="info-table">
            <tbody>
            <tr>
                <td>订单状态</td>
                <td>
                    {switch name="row.status"}
                    {case value="0"}<span class="status-label pending">待支付</span>{/case}
                    {case value="1"}<span class="status-label waiting">待接单</span>{/case}
                    {case value="2"}<span class="status-label departing">待出发</span>{/case}
                    {case value="3"}<span class="status-label starting">待开始</span>{/case}
                    {case value="4"}<span class="status-label in-service">服务中</span>{/case}
                    {case value="5"}<span class="status-label to-review">待评价</span>{/case}
                    {case value="6"}<span class="status-label completed">已完成</span>{/case}
                    {case value="7"}<span class="status-label cancelled">已取消</span>{/case}
                    {/switch}
                </td>
            </tr>
            <tr>
                <td>订单类型</td>
                <td>
                    {switch name="row.kind"}
                    {case value="DOOR"}<span class="type-label door-service">上门服务</span>{/case}
                    {case value="SHOP"}<span class="type-label shop-service">到店服务</span>{/case}
                    {/switch}
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td>{$row.remark}</td>
            </tr>
            <tr>
                <td>预约服务时间</td>
                <td>{$row.server_time?datetime($row.server_time):''}</td>
            </tr>
            <tr>
                <td>下单时间</td>
                <td>{$row.create_time?datetime($row.create_time):''}</td>
            </tr>
            <tr>
                <td>付款时间</td>
                <td>{$row.pay_time?datetime($row.pay_time):''}</td>
            </tr>
            <tr>
                <td>接单时间</td>
                <td>{$row.get_order_time?datetime($row.get_order_time):''}</td>
            </tr>
            <tr>
                <td>出发时间</td>
                <td>{$row.set_out_order_time?datetime($row.set_out_order_time):''}</td>
            </tr>
            <tr>
                <td>开始服务</td>
                <td>{$row.start_order_time?datetime($row.start_order_time):''}</td>
            </tr>
            <tr>
                <td>完成服务</td>
                <td>{$row.end_order_time?datetime($row.end_order_time):''}</td>
            </tr>
            <tr>
                <td>取消订单</td>
                <td>{$row.close_order_time?datetime($row.close_order_time):''}</td>
            </tr>
            <tr>
                <td>技师打卡图片</td>
                <td><img class="img-sm img-center" data-tips-image src="{$row.server_image|cdnurl|htmlentities}" alt="技师打卡图片"></td>
            </tr>
            </tbody>
        </table>
    </div>
    <!-- 地址信息（仅上门服务显示） -->
    {if $row.kind == 'DOOR'}
    <div class="address-section">
        <h2 class="section-title">地址信息</h2>
        <table class="info-table">
            <tbody>
            <tr>
                <td>联系人</td>
                <td>{$row.address_json.username ?? '无'}</td>
            </tr>
            <tr>
                <td>性别</td>
                <td>
                    {switch name="row.address_json.gender"}
                    {case value="1"}<span class="gender-label male">男</span>{/case}
                    {case value="0"}<span class="gender-label female">女</span>{/case}
                    {/switch}
                </td>
            </tr>
            <tr>
                <td>联系电话</td>
                <td>{$row.address_json.mobile ?? '无'}</td>
            </tr>
            <tr>
                <td>服务地址</td>
                <td>{$row.address_json.address_info ?? '无'}</td>
            </tr>
            <tr>
                <td>服务地址图片</td>
                <td><img class="img-sm img-center" data-tips-image src="{$row.address_json.image|cdnurl|htmlentities}" alt="服务地址图片"></td>
            </tr>
            </tbody>
        </table>
    </div>
    {/if}
    <!--分佣情况-->
    <div class="order-info-section">
        <h2 class="section-title">预计分佣情况</h2>
        <table class="info-table">
            <tbody>
            <tr>
                <td>代理商分佣</td>
                <td>
                 金额：￥{$row.agent_price}
                </td>
            </tr>
            <tr>
                <td>渠道商分佣</td>
                <td>
                    金额：￥{$row.store_price}
                </td>
            </tr>
            <tr>
                <td>技师分佣</td>
                <td>
                   金额：￥{$row.technicians_price}
                </td>
            </tr>
            <tr>
                <td>门店分佣</td>
                <td>
                   金额：￥{$row.shop_price}
                </td>
            </tr>
            <tr>
                <td>加盟商分佣</td>
                <td>
                  金额：￥{$row.franchisees_price}
                </td>
            </tr>
            <tr>
                <td>平台分佣</td>
                <td>
                  金额：￥{$row.system_price}
                </td>
            </tr>
            </tbody>
        </table>
    </div>


</div>
</body>

</html>