<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Shop_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-shop_id" data-rule="required" min="0" data-source="shop/index" class="form-control selectpage" name="row[shop_id]" type="text" value="{$row.shop_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" data-rule="required" class="form-control" step="0.01" name="row[money]" type="number" value="{$row.money|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Before')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-before" data-rule="required" class="form-control" step="0.01" name="row[before]" type="number" value="{$row.before|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('After')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-after" data-rule="required" class="form-control" step="0.01" name="row[after]" type="number" value="{$row.after|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Kind')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-kind" data-rule="required" class="form-control selectpicker" name="row[kind]">
                {foreach name="kindList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.kind"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Memo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-memo" class="form-control" name="row[memo]" type="text" value="{$row.memo|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
