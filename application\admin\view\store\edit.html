<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" disabled data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_name" data-rule="required" class="form-control" name="row[store_name]" type="text" value="{$row.store_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" data-rule="required" class="form-control" name="row[address]" type="text" value="{$row.address|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-info" data-rule="required" class="form-control" name="row[info]" type="text" value="{$row.info|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-store_image" data-rule="required" class="form-control" size="50" name="row[store_image]" type="text" value="{$row.store_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-store_image" class="btn btn-danger faupload" data-input-id="c-store_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-store_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-store_image" class="btn btn-primary fachoose" data-input-id="c-store_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-store_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-store_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" data-rule="required" class="form-control" name="row[money]" type="number" value="{$row.money|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('隶属加盟商')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-franchisees_id"  data-source="franchisees/index" data-field="real_name" class="form-control selectpage form-control" name="row[franchisees_id]" type="text" value="{$row.franchisees_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('身份证正面')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-id_card_front"  class="form-control" size="50" name="row[id_card_front]" type="text" value="{$row.id_card_front|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-id_card_front" class="btn btn-danger faupload" data-input-id="c-id_card_front" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-id_card_front"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-id_card_front" class="btn btn-primary fachoose" data-input-id="c-id_card_front" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-id_card_front"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-id_card_front"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('身份证反面')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-id_card_opposite"  class="form-control" size="50" name="row[id_card_opposite]" type="text" value="{$row.id_card_opposite|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-id_card_opposite" class="btn btn-danger faupload" data-input-id="c-id_card_opposite" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-id_card_opposite"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-id_card_opposite" class="btn btn-primary fachoose" data-input-id="c-id_card_opposite" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-id_card_opposite"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-id_card_opposite"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{$row.create_time ? $row.create_time : datetime(time())}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Update_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-update_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[update_time]" type="text" value="{$row.update_time ? $row.update_time : datetime(time())}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
