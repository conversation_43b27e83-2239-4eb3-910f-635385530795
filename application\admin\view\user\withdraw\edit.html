<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提现详情</title>
    <style>
        /* 全局样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f7fa;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        /* 主容器样式，用于包裹提现详情和装饰卡片 */
        .main-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }

        /* 提现详情容器 */
        .withdrawal-details {
            flex: 1;
            min-width: 300px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        /* 标题样式 */
        .details-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        /* 信息项样式 */
        .info-item {
            margin-bottom: 15px;
        }

        .info-label {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .info-value {
            color: #666;
        }

        /* 标签样式 */
        .label {
            padding: 4px 8px;
            border-radius: 4px;
            color: #fff;
            font-size: 12px;
        }

        .label-info {
            background-color: #5bc0de;
        }

        .label-warning {
            background-color: #f0ad4e;
        }

        .label-primary {
            background-color: #337ab7;
        }

        /* 底部按钮样式 */
        .button-container {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            margin: 0 5px;
        }

        .btn-primary {
            background-color: #337ab7;
            color: #fff;
        }

        .btn-primary:hover {
            background-color: #286090;
        }

        .btn-danger {
            background-color: #d9534f;
            color: #fff;
        }

        .btn-danger:hover {
            background-color: #c9302c;
        }

        .btn-embossed {
            box-shadow: 0 2px 0 rgba(0, 0, 0, 0.1);
        }

        /* 右侧装饰卡片样式 */
        .decorative-cards {
            flex: 0 0 250px;
            display: flex;
            flex-direction: column;
        }

        .decorative-card {
            background: linear-gradient(to bottom right, #e0f7fa, #b2ebf2);
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px;
            margin-bottom: 20px;
        }

        .decorative-card h3 {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .decorative-card p {
            font-size: 14px;
            color: #666;
        }

        /* 小屏幕适配 */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .withdrawal-details {
                min-width: auto;
            }

            .decorative-cards {
                flex: 1;
            }
        }
    </style>
</head>

<body>

<input type="hidden" id="ids" value="{$row.id}">
<div class="main-container">
    <div class="withdrawal-details">
        <div class="details-title">提现详情</div>
        <div class="info-item">
            <div class="info-label">昵称</div>
            <div class="info-value">{$row.nickname}</div>
        </div>
        <div class="info-item">
            <div class="info-label">提现方式</div>
            <div class="info-value">
                {switch name="row.kind"}
                {case value="WECHAT"}<span class="label label-info">微信</span>{/case}
                {case value="ALIPAY"}<span class="label label-warning">支付宝</span>{/case}
                {case value="BANK"}<span class="label label-primary">银行卡</span>{/case}
                {/switch}
            </div>
        </div>
        {if $row.kind == 'BANK'}
        <div class="info-item">
            <div class="info-label">币种</div>
            <div class="info-value">{$row.currency_name}</div>
        </div>
        {/if}
        <div class="info-item">
            <div class="info-label">申请提现金额</div>
            <div class="info-value">￥{$row.money}</div>
        </div>
        <div class="info-item">
            <div class="info-label">汇率</div>
            <div class="info-value">{$row.rate}</div>
        </div>
        <div class="info-item">
            <div class="info-label">换算人民币金额</div>
            <div class="info-value">￥{$row.rmb_money}</div>
        </div>
        <div class="info-item">
            <div class="info-label">手续费</div>
            <div class="info-value">￥{$row.commission}</div>
        </div>
        <div class="info-item" style="color: red">
            <div class="info-label">实际到账</div>
            <div class="info-value">￥{$row.real_money}</div>
        </div>
        <div class="info-item">
            <div class="info-label">提现账号</div>
            <div class="info-value">{$row.account}</div>
        </div>
        <div class="info-item">
            <div class="info-label">{if $row.kind == 'BANK'}开户行{else/}姓名{/if}</div>
            <div class="info-value">{$row.name}</div>
        </div>
        <div class="info-item">
            <div class="info-label">真实姓名</div>
            <div class="info-value">{$row.real_name}</div>
        </div>
        <div class="info-item">
            <div class="info-label">状态</div>
            <div class="info-value">
                {switch name="row.status"}
                {case value="created"}<span class="label label-info">申请中</span>{/case}
                {case value="successed"}<span class="label label-warning">成功</span>{/case}
                {case value="rejected"}<span class="label label-danger">已拒绝</span>{/case}
                {/switch}
            </div>
        </div>
        {if $row.status === 'created'}
        <div class="button-container">
            <button type="reset" class="btn btn-primary btn-embossed btn-close" id="pass">
                {:__('同意')}
            </button>
            <button type="reset" class="btn btn-danger btn-embossed btn-close" id="refuse">
                {:__('拒绝')}
            </button>
        </div>
        {/if}
    </div>
    <div class="decorative-cards">
        <div class="decorative-card">
            <h3>线下打款须知</h3>
            <p>同意提现申请后，请在 2 个工作日内完成线下打款操作。</p>
        </div>
        <div class="decorative-card">
            <h3>信息核对提示</h3>
            <p>打款前请仔细核对提现账号和开户行信息，避免打款错误。</p>
        </div>
    </div>
</div>
</body>

</html>