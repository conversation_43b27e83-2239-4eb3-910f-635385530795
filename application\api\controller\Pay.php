<?php

namespace app\api\controller;

use addons\epay\library\Service;
use app\common\controller\Api;
use app\common\model\Agent;
use app\common\model\RechargeOrder;
use app\common\model\Reward;
use app\common\model\Shop;
use app\common\model\ShopOrderMoneyLog;
use app\common\model\Store;
use app\common\model\Technicians;
use app\common\model\User;
use app\common\model\Withdraw;
use app\job\AdminNotice;
use app\job\OrderCoupon;
use app\job\UserGetLevel;
use fast\HWechat;
use PDOException;
use think\Db;
use think\Env;
use think\Exception;
use think\exception\DbException;
use think\Log;
use think\Queue;
use wechat\WechatWithDraw;

/**
 * 支付接口
 */
class Pay extends Api
{
    protected $noNeedLogin = ['notify', 'withdrawNotifyx'];
    protected $noNeedRight = ['*'];


    protected $params;

    protected function _initialize()
    {
        $this->params = [
            'type'      => 'wechat',
            'method'    => 'mp',
            'notifyurl' => $this->request->domain() . '/api/pay/notify/paytype/wechat'
        ];
        parent::_initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 支付接口
     * @param $orderId
     * @param string $payType
     * @return void
     * @throws DbException
     */
    public function pay($orderId, string $payType = 'WECHAT')
    {
        $prefix    = $this->getOrderPrefix($orderId);
        $orderInfo = $this->getOrderInfo($prefix, $orderId);
        if (!$orderInfo) {
            $this->error('订单错误');
        }
        if ($orderInfo->status != '0') {
            $this->error('该订单已支付');
        }
        $this->setPaymentParams($prefix, $orderInfo, $payType);
        switch ($payType) {
            case "YUE":
                if ($this->auth->money < $this->params['amount']) {
                    $this->error('余额不足');
                }
                $pay = $this->YUEPay();
                break;
            case "WECHAT":
                if (!$this->auth->openid) {
                    $this->error('未获取微信授权');
                }
                $this->params = array_merge($this->params, ['openid' => $this->auth->openid]);
                $pay          = Service::submitOrder($this->params);
                break;
            default:
                $this->error('支付失败，订单信息有误');
        }
        $this->success('支付成功', compact('pay', 'orderId'));
    }

    /**
     * 获取订单前缀
     * @param string $orderId
     * @return string
     */
    private function getOrderPrefix(string $orderId): string
    {
        return substr($orderId, 0, 1) ?? '';
    }

    /**
     * 获取订单信息
     * @param string $prefix
     * @param string $orderId
     * @return mixed
     * @throws DbException
     */
    private function getOrderInfo(string $prefix, string $orderId)
    {
        switch ($prefix) {
            case 'R':
                return RechargeOrder::get(['orderId' => $orderId]);
            case 'O':
                return \app\common\model\Order::get(['orderId' => $orderId]);
            case 'D':
                return Reward::get(['orderId' => $orderId]);
            case 'F':
                return \app\common\model\Franchisees::get(['orderId' => $orderId]);
            default:
                return null;
        }
    }

    /**
     * 设置支付参数
     * @param string $prefix
     * @param $orderInfo
     * @return void
     */
    private function setPaymentParams(string $prefix, $orderInfo, $payType = 'WECHAT')
    {
        switch ($prefix) {
            case 'R':
                $this->params = array_merge($this->params, [
                    'orderid' => $orderInfo->orderId,
                    'title'   => '用户充值',
                    'amount'  => $orderInfo->recharge_price
                ]);
                break;
            case 'O':
                $orderInfo->save(['pay_type' => $payType]);
                $this->params = array_merge($this->params, [
                    'orderid' => $orderInfo->orderId,
                    'title'   => '用户下单',
                    'amount'  => $orderInfo->total_price
                ]);
                break;
            case 'D':
                $this->params = array_merge($this->params, [
                    'orderid' => $orderInfo->orderId,
                    'title'   => '用户打赏',
                    'amount'  => $orderInfo->price
                ]);
                break;
            case 'F':
                $this->params = array_merge($this->params, [
                    'orderid' => $orderInfo->orderId,
                    'title'   => '申请成为加盟商',
                    'amount'  => $orderInfo->price
                ]);
                break;
        }
    }

    /**
     * 余额支付
     * @throws DbException
     */
    public function YUEPay()
    {
        $orderId   = $this->params['orderid'];
        $prefix    = self::getOrderPrefix($orderId);
        $orderInfo = self::getOrderInfo($prefix, $orderId);
        switch ($prefix) {
            case "D":
                $msg = '用户打赏';
                break;
            default:
                $msg = '用户下单';
                break;
        }
        #扣除用户余额
        \app\common\model\User::money(-$this->params['amount'], $orderInfo->user_id, $msg, 'USE');
        return $this->notifyMiddle($orderId, $this->params['amount']);
    }

    /**
     * 支付回调中间件
     * @param $out_trade_no
     * @param int $payamount
     * @param string $transaction_id
     * @return bool
     * @throws DbException
     */
    public function notifyMiddle($out_trade_no, $payamount = 0, $transaction_id = ''): bool
    {
        $prefix = $this->getOrderPrefix($out_trade_no);
        switch ($prefix) {
            case 'R':
                $this->rechargeNotify($out_trade_no, $payamount, $transaction_id);
                break;
            case 'O':
                $this->orderNotify($out_trade_no, $payamount, $transaction_id);
                break;
            case 'D':
                $this->rewardNotify($out_trade_no, $payamount, $transaction_id);
                break;
            case 'F':
                $this->franchiseesNotify($out_trade_no, $payamount, $transaction_id);
                break;
            default:
                return false;
        }
        return true;
    }

    /**
     * 支付成功回调
     */
    public function notify()
    {
        $paytype = $this->request->param('paytype');
        $pay     = Service::checkNotify($paytype);
        if (!$pay) {
            return json(['code' => 'FAIL', 'message' => '失败'], 500, ['Content-Type' => 'application/json']);
        }
        // 获取回调数据，V3和V2的回调接收不同
        $data = Service::isVersionV3() ? $pay->callback() : $pay->verify();
        try {
            //微信支付V3返回和V2不同
            if (Service::isVersionV3() && $paytype === 'wechat') {
                $data              = $data['resource']['ciphertext'];
                $data['total_fee'] = $data['amount']['total'];
            }
            \think\Log::record($data);
            //获取支付金额、订单号
            $payamount    = $paytype == 'alipay' ? $data['total_amount'] : $data['total_fee'] / 100;
            $out_trade_no = $data['out_trade_no'];
            #回调逻辑
            $this->notifyMiddle($out_trade_no, $payamount, $data['transaction_id']);
            \think\Log::record("回调成功，订单号：{$out_trade_no}，金额：{$payamount}");
            //你可以在此编写订单逻辑
        } catch (Exception $e) {
            \think\Log::record("回调逻辑处理错误:" . $e->getMessage(), "error");
        }
        //下面这句必须要执行,且在此之前不能有任何输出
        if (Service::isVersionV3()) {
            return $pay->success()->getBody()->getContents();
        } else {
            return $pay->success()->send();
        }
    }


    /**
     * 充值回调
     * @param $out_trade_no
     * @param $payamount
     * @param $transaction_id
     * @return bool
     * @throws DbException
     */
    public function rechargeNotify($out_trade_no, $payamount = 0, $transaction_id = ''): bool
    {
        $rechargeOrderInfo = (new RechargeOrder())->get(['orderId' => $out_trade_no]);
        if (!$rechargeOrderInfo || $rechargeOrderInfo->status != '0') {
            return false;
        }
        $totalPrice = bcadd($rechargeOrderInfo->recharge_price, $rechargeOrderInfo->give_price, 2);
        Db::startTrans();
        try {
            #会员加余额
            \app\common\model\User::money($totalPrice, $rechargeOrderInfo->user_id, '会员充值');
            #更新累计充值
            setIncByIdentity($rechargeOrderInfo->user_id, 'total_recharge_price', $totalPrice);
            #更新订单状态
            $rechargeOrderInfo->save(['status' => '1', 'pay_price' => $payamount, 'transaction_id' => $transaction_id, 'pay_time' => time()]);
            Db::commit();
        } catch (Exception$exception) {
            Db::rollback();
            \think\Log::record("充值回调逻辑处理错误:" . $exception->getMessage(), "error");
        }
        return true;
    }

    /**
     * 下单支付回调
     * @param $out_trade_no
     * @param $payamount
     * @param $transaction_id
     * @return bool
     * @throws DbException
     */
    public function orderNotify($out_trade_no, $payamount = 0, $transaction_id = '')
    {
        $orderInfo = (new \app\common\model\Order())->get(['orderId' => $out_trade_no]);
        if (!$orderInfo || $orderInfo->status != '0') {
            return false;
        }
        Db::startTrans();
        try {
            #如果是更换技师订单
            if ($orderInfo->p_order_id != 0) {
                $pOrderInfo = (new \app\common\model\Order())->get($orderInfo->p_order_id);
                if ($pOrderInfo) {
                    $pOrderInfo->save(['status' => 7, 'close_order_time' => time()]);
                    #将路费补给技师
                    Technicians::money($pOrderInfo->travel_price, $pOrderInfo->technicians_id, '路费到账', 'CAR');
                    #更新累计收益
                    setIncByIdentity($pOrderInfo->technicians_id, 'total_car_money', $pOrderInfo->travel_price, 'TECHNICIANS');
                    #推送给技师刷新订单
                    sendInformationToUser((new Technicians())->getUserIdByTechniciansId($pOrderInfo->technicians_id), 'order');
                }
            }
            $orderInfo->save(['status' => '1', 'pay_price' => $payamount, 'pay_time' => time(), 'transaction_id' => $transaction_id]);
            #消耗优惠券
            if ($orderInfo->user_coupon_log_id) {
                Queue::push(OrderCoupon::class, ['user_coupon_log_id' => $orderInfo->user_coupon_log_id]);
            }
            #给技师推送消息
            Queue::push(AdminNotice::class, [
                'is_all'          => '5',
                'technicians_ids' => $orderInfo->technicians_id,
                'name'            => '订单通知',
                'content'         => '您有新的订单，请注意查收：' . $orderInfo->orderId,
                'kind'            => 'ORDER',
                'order_id'        => $orderInfo->id
            ]);
            #推送公众号消息
            (new HWechat)->init(get_addon_config('epay')['wechat'])->sendReadyToTechniciansAndShop($orderInfo->id);
            #长连接推送
            (new \app\common\model\Order())->updateOrderInfoStatus($orderInfo->id);
            Db::commit();
        } catch (Exception$exception) {
            Db::rollback();
            \think\Log::record("下单回调逻辑处理错误:" . $exception->getMessage(), "error");
        }
        return true;
    }

    /**
     * 打赏回调
     * @param $out_trade_no
     * @param $payamount
     * @param $transaction_id
     * @return bool
     * @throws DbException
     */
    public function rewardNotify($out_trade_no, $payamount = 0, $transaction_id = '')
    {
        $rewardInfo = (new \app\common\model\Reward())->get(['orderId' => $out_trade_no]);
        if (!$rewardInfo || $rewardInfo->status != 0) {
            return false;
        }
        Db::startTrans();
        try {
            #更改订单状态
            $rewardInfo->save(['status' => 1, 'transaction_id' => $transaction_id]);
            #更改技师余额
            Technicians::money($rewardInfo->technicians_price, $rewardInfo->technicians_id, '用户打赏', 'REWARD', $rewardInfo->order_id);
            #更新技师累计收到的打赏
            setIncByIdentity($rewardInfo->technicians_id, 'total_reward_money', $rewardInfo->technicians_price, 'TECHNICIANS');
            #更新用户累计打赏金额
            setIncByIdentity($rewardInfo->user_id, 'total_give_price', $rewardInfo->price);
            #更新用户等级和累计消费
            Queue::push(UserGetLevel::class, ['user_id' => $rewardInfo->user_id, 'pay_price' => $rewardInfo->price]);
            #更新订单累计打赏金额
            $orderInfo = \app\common\model\Order::get($rewardInfo->order_id);
            if ($orderInfo) {
                $orderInfo->setInc('reward_price', $rewardInfo->technicians_price);
                $orderInfo->setInc('real_reward_price', $rewardInfo->price);
            }
            #更新门店打赏收益记录
            $shopOrderMoneyLog = ShopOrderMoneyLog::get(['order_id' => $orderInfo->id]);
            if ($shopOrderMoneyLog) {
                $shopOrderMoneyLog->setInc('reward_money', $rewardInfo->technicians_price);
                $shopOrderMoneyLog->setInc('real_reward_money', $rewardInfo->price);
            }
            Db::commit();
        } catch (Exception$exception) {
            Db::rollback();
            \think\Log::record("打赏回调逻辑处理错误:" . $exception->getMessage(), "error");
        }
        return true;
    }

    /**
     * 加盟商支付回调
     * @param $out_trade_no
     * @param $payamount
     * @param $transaction_id
     * @return bool
     * @throws DbException
     */
    public function franchiseesNotify($out_trade_no, $payamount = 0, $transaction_id = '')
    {
        $franchiseesInfo = (new \app\common\model\Franchisees())->get(['orderId' => $out_trade_no]);
        if (!$franchiseesInfo) {
            return false;
        }
        Db::startTrans();
        try {
            #更改订单状态
            $franchiseesInfo->save(['pay_price' => $payamount, 'transaction_id' => $transaction_id, 'pay_time' => time()]);
            Db::commit();
        } catch (Exception$exception) {
            Db::rollback();
            \think\Log::record("加盟商回调逻辑处理错误:" . $exception->getMessage(), "error");
        }
        return true;
    }

    /**
     * 微信提现回调
     * @return false|string
     * @throws DbException
     */
    public function withdrawNotifyx()
    {
        $param           = $this->request->param();
        $associated_data = $param['resource']['associated_data'];
        $nonce           = $param['resource']['nonce'];
        $ciphertext      = $param['resource']['ciphertext'];
        $success         = (new \fast\WechatWithDraw())->decryptToString($associated_data, $nonce, $ciphertext);
        $array_data      = json_decode($success, true);
        $orderId         = $array_data['out_bill_no'];
        $order           = Withdraw::get(['orderid' => $orderId]);
        if (!$order) {
            $this->error('', ['code' => 'ERROR', 'msg' => ''], 409);
        }
        if (!in_array($order->status, ['created', 'successed'])) {
            $this->error('', ['code' => 'ERROR', 'msg' => ''], 409);
        }
        Db::startTrans();
        try {
            if ($array_data['state'] == 'SUCCESS') {
                #转账成功
                $order->status = 'successed';
            } else if ($array_data['state'] == 'FAIL') {
                //转账失败，退回余额
                $order->status = 'fail';
                switch ($order->identity) {
                    case 'USER':
                        User::money($order->rmb_money, $order->withdrawer_id, '提现失败', 'REFUND');
                        break;
                    case 'TECHNICIANS':
                        Technicians::money($order->rmb_money, $order->withdrawer_id, '提现失败');
                        break;
                    case 'AGENT':
                        Agent::money($order->rmb_money, $order->withdrawer_id, '提现失败');
                        break;
                    case 'STORE':
                        Store::money($order->rmb_money, $order->withdrawer_id, '提现失败');
                        break;
                    case 'SHOP':
                        Shop::money($order->rmb_money, $order->withdrawer_id, '提现失败');
                        break;
                }
            }
            $order->is_profile   = 1;
            $order->transfertime = time();
            $order->save();
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error('', ['code' => 'ERROR', 'msg' => $e->getMessage()], 409);
        }
        return json_encode(['code' => 'SUCCESS', 'msg' => 'OK']);
    }
}
