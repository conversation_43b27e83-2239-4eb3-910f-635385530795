<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Recharge as RechargeModel;
use app\common\model\RechargeOrder;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

/**
 * 充值管理
 */
class Recharge extends Api
{

    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [''];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];


    /**
     * 充值列表
     * @param RechargeModel $rechargeModel
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getRechargeSetList(RechargeModel $rechargeModel)
    {
        $list = $rechargeModel->getList();
        $this->success('ok', $list);
    }

    /**
     * 创建支付订单
     * @param RechargeOrder $rechargeOrderModel
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function createOrder(RechargeOrder $rechargeOrderModel)
    {
        $result = $rechargeOrderModel->createOrder(input());
        if (!$result) {
            $this->error($rechargeOrderModel->getError());
        }
        $this->success('订单创建成功', $result);
    }

    /**
     * 关闭订单
     * @param RechargeOrder $rechargeOrderModel
     * @return void
     * @throws DbException
     */
    public function closeOrder(RechargeOrder $rechargeOrderModel)
    {
        $result = $rechargeOrderModel->closeOrder(input('recharge_order_id'));
        if (!$result) {
            $this->error($rechargeOrderModel->getError());
        }
        $this->success('操作成功', $result);
    }

}
