<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Dynamic;
use app\common\model\ShopMoneyLog;
use app\common\model\ShopOrderMoneyLog;
use app\common\model\TechniciansServer;
use app\common\model\Withdraw;
use Endroid\QrCode\Exception\InvalidPathException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

/**
 * 门店接口
 */
class Shop extends Api
{
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];


    /**
     * 门店入驻
     * @param \app\common\model\Shop $shopModel
     * @return void
     * @throws DbException
     */
    public function createShop(\app\common\model\Shop $shopModel)
    {
        $result = $shopModel->createShop(input());
        if (!$result) {
            $this->error($shopModel->getError());
        }
        $this->success('提交成功');
    }

    /**
     * 查看入驻详情
     * @param \app\common\model\Shop $shopModel
     * @return void
     * @throws DbException
     */
    public function checkShop(\app\common\model\Shop $shopModel)
    {
        $row = $shopModel->getSettledInfo();
        if (!$row) {
            $this->error('未入驻');
        }
        $this->success('ok', $row);
    }


    /**
     * 指定店铺详情
     * @param \app\common\model\Shop $shopModel
     * @return void
     * @throws DbException
     */
    public function assignShopInfo(\app\common\model\Shop $shopModel)
    {
        $row = $shopModel->assignShopInfo(input('shop_id'));
        if (!$row) {
            $this->error($shopModel->getError());
        }
        $this->success('ok', $row);
    }

    /**
     * 发布动态
     * @param Dynamic $dynamicModel
     * @return void
     */
    public function createDynamic(Dynamic $dynamicModel)
    {
        $result = $dynamicModel->createDynamic(input());
        if (!$result) {
            $this->error($dynamicModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 动态列表
     * @param Dynamic $dynamicModel
     * @return void
     * @throws DbException
     */
    public function dynamicList(Dynamic $dynamicModel)
    {
        $this->success('ok', $dynamicModel->getList(['user_id' => $this->auth->id]));
    }

    /**
     * 动态详情
     * @param Dynamic $dynamicModel
     * @return void
     * @throws DbException
     */
    public function dynamicInfo(Dynamic $dynamicModel)
    {
        $result = $dynamicModel->getInfo(input('dynamic_id'));
        if (!$result) {
            $this->error($dynamicModel->getError());
        }
        $this->success('ok', $result);
    }

    /**
     * 删除动态
     * @param Dynamic $dynamicModel
     * @return void
     */
    public function delDynamic(Dynamic $dynamicModel)
    {
        $this->success('删除成功', $dynamicModel->delDynamic(input('dynamic_id')));
    }


    /**
     * 技师下拉列表
     * @param \app\common\model\Technicians $techniciansModel
     * @param \app\common\model\Shop $shopModel
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function techniciansSelect(\app\common\model\Technicians $techniciansModel, \app\common\model\Shop $shopModel)
    {
        $shopInfo = $shopModel->getSettledInfo();
        $this->success('ok', $techniciansModel->getTechniciansListByShopId($shopInfo->id ?? '', 100000000000));
    }


    /**
     * 首页技师列表
     * @param \app\common\model\Shop $shopModel
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function techniciansList(\app\common\model\Shop $shopModel)
    {
        $shopInfo = $shopModel->getSettledInfo();
        $this->success('ok', $shopModel->getTechniciansList($shopInfo->id));
    }

    /**
     * 技师管理技师列表
     * @param \app\common\model\Shop $shopModel
     * @return void
     * @throws DbException
     */
    public function techniciansSetList(\app\common\model\Shop $shopModel)
    {
        $shopInfo = $shopModel->getSettledInfo();
        $this->success('ok', $shopModel->techniciansSetList($shopInfo->id));
    }


    /**
     * 添加技师
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function createTechnicians(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->createTechnicians(input(), true);
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('提交成功');
    }

    /**
     * 我的服务列表
     * @param TechniciansServer $techniciansServerModel
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function serverList(TechniciansServer $techniciansServerModel)
    {
        $this->success('ok', $techniciansServerModel->getList(input()));
    }

    /**
     * 添加服务
     * @param TechniciansServer $techniciansServerModel
     * @return void
     * @throws DbException
     */
    public function createServer(TechniciansServer $techniciansServerModel)
    {
        $result = $techniciansServerModel->saveServer(input());
        if (!$result) {
            $this->error($techniciansServerModel->getError());
        }
        $this->success('操作成功');
    }


    /**
     * 编辑服务
     * @param TechniciansServer $techniciansServerModel
     * @return void
     * @throws DbException
     */
    public function updateServer(TechniciansServer $techniciansServerModel)
    {
        $result = $techniciansServerModel->saveServer(input(), false);
        if (!$result) {
            $this->error($techniciansServerModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 删除服务
     * @param TechniciansServer $techniciansServerModel
     * @return void
     * @throws DbException
     */
    public function delServer(TechniciansServer $techniciansServerModel)
    {
        $result = $techniciansServerModel->delServer(input('technicians_server_id'));
        if (!$result) {
            $this->error($techniciansServerModel->getError());
        }
        $this->success('删除成功');
    }


    /**
     * 分配技师账号
     * @param \app\common\model\Shop $shopModel
     * @return void
     * @throws DbException
     * @throws InvalidPathException
     */
    public function createTechniciansAccount(\app\common\model\Shop $shopModel)
    {
        $result = $shopModel->createTechniciansAccount(input('technicians_id'));
        if (!$result) {
            $this->error($shopModel->getError());
        }
        $this->success('分配成功');
    }

    /**
     * 删除技师账号
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function delTechnicians(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->deleteAccount(input('technicians_id'));
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('删除成功');
    }

    /**
     * 技师详情
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function techniciansInfo(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->getTechniciansInfo(input('technicians_id'));
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('ok', $result);
    }

    /**
     * 修改技师资料
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function updateTechniciansInfo(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->updateTechniciansInfo(input(), true);
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 设置服务时间
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function saveServerTime(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->updateTechniciansInfo(input(), true);
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('操作成功');
    }


    /**
     * 收益管理
     * @param ShopOrderMoneyLog $shopOrderMoneyLogModel
     * @return void
     * @throws DbException
     */
    public function shopOrderMoneyLogList(ShopOrderMoneyLog $shopOrderMoneyLogModel)
    {
        $this->success('ok', $shopOrderMoneyLogModel->getList(input()));
    }


    /**
     * 门店余额变更记录
     * @param ShopMoneyLog $shopMoneyLogModel
     * @return void
     * @throws DbException
     */
    public function shopMoneyLogList(ShopMoneyLog $shopMoneyLogModel)
    {
        $this->success('ok', $shopMoneyLogModel->getList(input()));
    }

    /**
     * 申请提现
     * @param Withdraw $withdrawModel
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function createWithdraw(Withdraw $withdrawModel)
    {
        $result = $withdrawModel->createWithdraw(input(), 'SHOP');
        if (!$result) {
            $this->error($withdrawModel->getError());
        }
        $this->success('提交成功');
    }


    /**
     * 门店个人信息
     * @param \app\common\model\Shop $shopModel
     * @return void
     * @throws DbException
     */
    public function getShopInfo(\app\common\model\Shop $shopModel)
    {
        $result = $shopModel->getShopInfo();
        if (!$result) {
            $this->error($shopModel->getError(), '', 403);
        }
        $this->success('ok', $result);
    }

    /**
     * 修改分佣比例
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function updateTechniciansBrokerage(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->updateTechniciansBrokerage(input('technicians_id'), input('brokerage'));
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('操作成功');
    }


}
