<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\StoreOrderMoneyLog;
use PDOException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use app\common\model\Withdraw;

/**
 * 渠道商接口
 */
class Store extends Api
{
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];


    /**
     * 渠道商入驻
     * @param \app\common\model\Store $storeModel
     * @return void
     * @throws DbException
     */
    public function createStore(\app\common\model\Store $storeModel)
    {
        $result = $storeModel->createStore(input());
        if (!$result) {
            $this->error($storeModel->getError());
        }
        $this->success('提交成功');
    }

    /**
     * 查看入驻详情
     * @param \app\common\model\Store $storeModel
     * @return void
     * @throws DbException
     */
    public function checkStore(\app\common\model\Store $storeModel)
    {
        $row = $storeModel->getSettledInfo();
        if (!$row) {
            $this->error('未入驻');
        }
        $this->success('ok', $row);
    }

    /**
     * 收入明细
     * @param StoreOrderMoneyLog $storeOrderMoneyLogModel
     * @return void
     * @throws DbException
     */
    public function storeOrderMoneyList(StoreOrderMoneyLog $storeOrderMoneyLogModel)
    {
        $this->success('ok', $storeOrderMoneyLogModel->getList());
    }


    /**
     * 申请提现
     * @param Withdraw $withdrawModel
     * @return void
     * @throws DbException
     * @throws PDOException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function createWithdraw(Withdraw $withdrawModel)
    {
        $result = $withdrawModel->createWithdraw(input(),'STORE');
        if (!$result) {
            $this->error($withdrawModel->getError());
        }
        $this->success('提交成功');
    }

    /**
     * 门店个人信息
     * @param \app\common\model\Store $storeModel
     * @return void
     * @throws DbException
     */
    public function getStoreInfo(\app\common\model\Store $storeModel)
    {
        $result = $storeModel->getInfo();
        if (!$result) {
            $this->error($storeModel->getError(),'',403);
        }
        $this->success('ok', $result);
    }

    /**
     * 获取邀请码
     * @param \app\common\model\Store $storeModel
     * @return void
     * @throws DbException
     * @throws \Endroid\QrCode\Exception\InvalidPathException
     */
    public function getInviteUrl(\app\common\model\Store $storeModel)
    {
        $this->success('ok',$storeModel->getInviteUrl());
    }

}
