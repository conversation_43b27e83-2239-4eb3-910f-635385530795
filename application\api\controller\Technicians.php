<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Dynamic;
use app\common\model\Evaluate;
use app\common\model\TechniciansMoneyLog;
use app\common\model\TechniciansNotice;
use app\common\model\TechniciansOrderMoneyLog;
use app\common\model\TechniciansServer;
use app\common\model\UserNotice;
use app\common\model\Withdraw;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\exception\PDOException;

/**
 * 技师接口
 */
class Technicians extends Api
{
    protected $noNeedLogin = [''];
    protected $noNeedRight = ['*'];


    /**
     * 更新经纬度
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function updateLocation(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->updateTechniciansInfo(input());
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('ok');
    }


    /**
     * 申请提现
     * @param Withdraw $withdrawModel
     * @return void
     * @throws PDOException
     */
    public function createWithdraw(Withdraw $withdrawModel)
    {
        $result = $withdrawModel->createWithdraw(input(), 'TECHNICIANS');
        if (!$result) {
            $this->error($withdrawModel->getError());
        }
        $this->success('提交成功');
    }

    /**
     * 收入明细
     * @param TechniciansOrderMoneyLog $techniciansOrderMoneyLogModel
     * @return void
     * @throws DbException
     */
    public function techniciansOrderMoneyInfo(TechniciansOrderMoneyLog $techniciansOrderMoneyLogModel)
    {
        $result = $techniciansOrderMoneyLogModel->getInfo(input('technicians_order_money_log_id'));
        if (!$result) {
            $this->error($techniciansOrderMoneyLogModel->getError());
        }
        $this->success('ok', $result);
    }


    /**
     * 收入列表
     * @param TechniciansOrderMoneyLog $techniciansOrderMoneyLogModel
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function techniciansOrderMoneyList(TechniciansOrderMoneyLog $techniciansOrderMoneyLogModel, \app\common\model\Technicians $techniciansModel)
    {
        $techniciansInfo = $techniciansModel->getSettledInfo();
        $list            = $techniciansOrderMoneyLogModel->getList(['technicians_id' => $techniciansInfo->id]);
        $this->success('ok', $list);
    }


    /**
     * 提现记录
     * @param TechniciansMoneyLog $techniciansMoneyLogModel
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function techniciansWithdrawList(TechniciansMoneyLog $techniciansMoneyLogModel, \app\common\model\Technicians $techniciansModel)
    {
        $techniciansInfo = $techniciansModel->getSettledInfo();
        $list            = $techniciansMoneyLogModel->getList(['technicians_id' => $techniciansInfo->id, 'kind' => 'WITHDRAW']);
        $this->success('ok', $list);
    }


    /**
     * 我的评价
     * @param Evaluate $evaluateModel
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function evaluateList(Evaluate $evaluateModel, \app\common\model\Technicians $techniciansModel)
    {
        $techniciansInfo = $techniciansModel->getSettledInfo();
        $this->success('ok', $evaluateModel->getList(['technicians_id' => $techniciansInfo->id, 'kind' => 'USER']));
    }


    /**
     * 技师个人资料
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getInfo(\app\common\model\Technicians $techniciansModel)
    {
        $row = $techniciansModel->getInfo();
        if (!$row) {
            $this->error($techniciansModel->getError());
        }
        $this->success('ok', $row);
    }

    /**
     * 技师入驻
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function createTechnicians(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->createTechnicians(input());
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('提交成功');
    }

    /**
     * 查看入驻详情
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function checkTechnicians(\app\common\model\Technicians $techniciansModel)
    {
        $row = $techniciansModel->getSettledInfo();
        if (!$row) {
            $this->error('未入驻');
        }
        $this->success('ok', $row);
    }

    /**
     * 技师列表
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function techniciansList(\app\common\model\Technicians $techniciansModel)
    {
        $list = $techniciansModel->getTechniciansList(input());
        $this->success('ok', $list);
    }

    /**
     * 技师详情
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function techniciansInfo(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->getTechniciansInfo(input('technicians_id'), true);
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('ok', $result);
    }


    /**
     * 项目详情
     * @param TechniciansServer $techniciansServerModel
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function techniciansServerInfo(TechniciansServer $techniciansServerModel)
    {
        $result = $techniciansServerModel->getServerInfo(input('technicians_server_id'));
        if (!$result) {
            $this->error($techniciansServerModel->getError());
        }
        $this->success('ok', $result);
    }

    /**
     * 发布动态
     * @param Dynamic $dynamicModel
     * @return void
     */
    public function createDynamic(Dynamic $dynamicModel)
    {
        $result = $dynamicModel->createDynamic(input());
        if (!$result) {
            $this->error($dynamicModel->getError());
        }
        $this->success('发布成功');
    }

    /**
     * 删除动态
     * @param Dynamic $dynamicModel
     * @return void
     */
    public function delDynamic(Dynamic $dynamicModel)
    {
        $this->success('删除成功', $dynamicModel->delDynamic(input('dynamic_id')));
    }

    /**
     * 动态列表
     * @param Dynamic $dynamicModel
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function dynamicList(Dynamic $dynamicModel, \app\common\model\Technicians $techniciansModel)
    {
        $row = $techniciansModel->getSettledInfo();
        if (!$row) {
            $this->error('参数错误');
        }
        $this->success('ok', $dynamicModel->getList(['technicians_id' => $row->id]));
    }

    /**
     * 动态详情
     * @param Dynamic $dynamicModel
     * @return void
     * @throws DbException
     */
    public function dynamicInfo(Dynamic $dynamicModel)
    {
        $result = $dynamicModel->getInfo(input('dynamic_id'));
        if (!$result) {
            $this->error($dynamicModel->getError());
        }
        $this->success('ok', $result);
    }


    /**
     * 新消息提示
     * @param TechniciansNotice $techniciansNoticeModel
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getNewNotice(TechniciansNotice $techniciansNoticeModel)
    {
        $list = $techniciansNoticeModel->getNewNotice();
        $this->success('ok', $list);
    }

    /**
     * 消息列表
     * @param TechniciansNotice $techniciansNoticeModel
     * @return void
     * @throws DbException
     */
    public function getNoticeList(TechniciansNotice $techniciansNoticeModel)
    {
        $list = $techniciansNoticeModel->getList(input('kind'));
        $this->success('ok', $list);
    }

    /**
     * 消息详情
     * @param TechniciansNotice $techniciansNoticeModel
     * @return void
     * @throws DbException
     */
    public function getNoticeInfo(TechniciansNotice $techniciansNoticeModel)
    {
        $row = $techniciansNoticeModel->getInfo(input('notice_id'));
        $this->success('ok', $row);
    }

    /**
     * 修改技师资料
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function updateTechniciansInfo(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->updateTechniciansInfo(input());
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 添加服务
     * @param TechniciansServer $techniciansServerModel
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function createServer(TechniciansServer $techniciansServerModel, \app\common\model\Technicians $techniciansModel)
    {
        $params          = input();
        $techniciansInfo = $techniciansModel->getSettledInfo();
        if (!$techniciansInfo) {
            $this->error('参数错误');
        }
        $params['technicians_id'] = $techniciansInfo->id;
        $result                   = $techniciansServerModel->saveServer($params);
        if (!$result) {
            $this->error($techniciansServerModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 编辑服务
     * @param TechniciansServer $techniciansServerModel
     * @return void
     * @throws DbException
     */
    public function updateServer(TechniciansServer $techniciansServerModel)
    {
        $result = $techniciansServerModel->saveServer(input(), false);
        if (!$result) {
            $this->error($techniciansServerModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 我的服务列表
     * @param TechniciansServer $techniciansServerModel
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function serverList(TechniciansServer $techniciansServerModel, \app\common\model\Technicians $techniciansModel)
    {
        $techniciansInfo = $techniciansModel->getSettledInfo();
        if (!$techniciansInfo) {
            $this->error('参数错误');
        }
        $this->success('ok', $techniciansServerModel->getList(['technicians_id' => $techniciansInfo->id]));
    }

    /**
     * 删除服务
     * @param TechniciansServer $techniciansServerModel
     * @return void
     * @throws DbException
     */
    public function delServer(TechniciansServer $techniciansServerModel)
    {
        $result = $techniciansServerModel->delServer(input('technicians_server_id'));
        if (!$result) {
            $this->error($techniciansServerModel->getError());
        }
        $this->success('删除成功');
    }

    /**
     * 设置服务时间
     * @param \app\common\model\Technicians $techniciansModel
     * @return void
     * @throws DbException
     */
    public function saveServerTime(\app\common\model\Technicians $techniciansModel)
    {
        $result = $techniciansModel->updateTechniciansInfo(input());
        if (!$result) {
            $this->error($techniciansModel->getError());
        }
        $this->success('操作成功');
    }

}
