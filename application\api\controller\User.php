<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\Collect;
use app\common\model\Dynamic;
use app\common\model\Evaluate;
use app\common\model\Feedback;
use app\common\model\FeedbackSet;
use app\common\model\InviteLog;
use app\common\model\Order;
use app\common\model\UserCouponLog;
use app\common\model\UserDynamicLikes;
use app\common\model\UserMoneyLog;
use app\common\model\UserNotice;
use app\common\model\UserWithdrawAccount;
use app\common\model\VipSet;
use app\common\model\Withdraw;
use Endroid\QrCode\Exception\InvalidPathException;
use Exception;
use fast\Random;
use GatewayClient\Gateway;
use think\Config;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Log;
use think\Validate;
use fast\Wechat;

/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third', 'WechatLoginH5', 'bindOpenid'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }
    }

    /**
     * 提现轮询接口
     * @return void
     */
    public function getWithdrawInfo()
    {
        $this->success('ok', (new Withdraw())->where(['user_id' => $this->auth->id, 'is_profile' => 0])->value('package_info'));
    }

    /**
     * 更新经纬度
     * @param \app\common\model\User $userModel
     * @return void
     * @throws DbException
     */
    public function updateLocation(\app\common\model\User $userModel)
    {
        $result = $userModel->updateUserLocation(input());
        if (!$result) {
            $this->error($userModel->getError());
        }
        $this->success('ok');
    }

    /**
     * 获取邀请链接
     * @return void
     * @throws InvalidPathException
     */
    public function getInviteUrl()
    {
        $user = $this->auth->getUser();
        if (!$user->invite_url) {
            $user->invite_url = generateQrcode($user->id);
            $user->save();
        }
        $this->success('ok', $user->invite_url);
    }

    /**
     * 绑定用户
     * @return void
     */
    public function bindClientId()
    {
        $clientId = input('client_id');
        if ($clientId) {
            Gateway::bindUid($clientId, $this->auth->id);
        }
        $this->success();
    }


    /**
     * 收藏/取消收藏
     * @param Collect $collectModel
     * @return void
     * @throws DbException
     */
    public function saveCollect(Collect $collectModel)
    {
        $result = $collectModel->saveCollect(input());
        if (!$result) {
            $this->error($collectModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 收藏列表
     * @param Collect $collectModel
     * @return void
     */
    public function collectList(Collect $collectModel)
    {
        $this->success('ok', $collectModel->getList());
    }

    /**
     * 批量删除
     * @param Collect $collectModel
     * @return void
     */
    public function delCollect(Collect $collectModel)
    {
        $collectModel->del(input('technicians_ids'));
        $this->success('操作成功');
    }


    /**
     * 动态列表
     * @param Dynamic $dynamicModel
     * @return void
     * @throws DbException
     */
    public function dynamicList(Dynamic $dynamicModel)
    {
        $this->success('ok', $dynamicModel->getList(input()));
    }


    /**
     * 查看某个技师动态列表
     * @param Dynamic $dynamicModel
     * @return void
     * @throws DbException
     */
    public function viewOneDynamicList(Dynamic $dynamicModel)
    {
        $techniciansId = input('technicians_id');
        if (!$techniciansId) $this->error('技师参数错误');
        $this->success('ok', $dynamicModel->getList(['technicians_id' => $techniciansId]));
    }


    /**
     * 查看技师评价列表
     * @param Evaluate $evaluateModel
     * @return void
     * @throws DbException
     */
    public function techniciansEvaluateList(Evaluate $evaluateModel)
    {
        $kind           = 'USER';
        $technicians_id = input('technicians_id');
        $list           = $evaluateModel->getList(compact('technicians_id', 'kind'));
        if (!$list) {
            $this->error($evaluateModel->getError());
        }
        $this->success('ok', $list);
    }

    /**
     * 服务项目评价
     * @param Evaluate $evaluateModel
     * @return void
     * @throws DbException
     */
    public function serverEvaluateList(Evaluate $evaluateModel)
    {
        $kind           = 'USER';
        $server_id      = input('server_id');
        $technicians_id = input('technicians_id');
        $list           = $evaluateModel->getList(compact('server_id', 'kind', 'technicians_id'));
        if (!$list) {
            $this->error($evaluateModel->getError());
        }
        $this->success('ok', $list);
    }

    /**
     * 点赞/取消点赞
     * @param UserDynamicLikes $userDynamicLikesModel
     * @return void
     * @throws DbException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function likesDynamic(UserDynamicLikes $userDynamicLikesModel)
    {
        $result = $userDynamicLikesModel->setIncOrSetDec($this->auth->id, input('dynamic_id'));
        if (!$result) {
            $this->error($userDynamicLikesModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 确认解决
     * @param Feedback $feedbackModel
     * @return void
     * @throws DbException
     */
    public function confirmFeedback(Feedback $feedbackModel)
    {
        $result = $feedbackModel->confirmFeedback(input('feedback_id'));
        if (!$result) {
            $this->error($feedbackModel->getError());
        }
        $this->success('操作成功');
    }

    /**
     * 反馈记录详情
     * @param Feedback $feedbackModel
     * @return void
     * @throws DbException
     */
    public function feedbackInfo(Feedback $feedbackModel)
    {
        $result = $feedbackModel->getInfo(input('feedback_id'));
        if (!$result) {
            $this->error($feedbackModel->getError());
        }
        $this->success('ok', $result);
    }

    /**
     * 反馈记录
     * @param Feedback $feedbackModel
     * @return void
     * @throws DbException
     */
    public function feedbackList(Feedback $feedbackModel)
    {
        $this->success('ok', $feedbackModel->getList(input()));
    }

    /**
     * 提交反馈
     * @param Feedback $feedbackModel
     * @return void
     * @throws PDOException|DbException
     */
    public function createFeedback(Feedback $feedbackModel)
    {
        $result = $feedbackModel->createFeedback(input());
        if (!$result) {
            $this->error($feedbackModel->getError());
        }
        $this->success('提交成功');
    }

    /**
     * 反饋類型
     * @param FeedbackSet $feedbackSetModel
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws DbException
     */
    public function feedbackSetList(FeedbackSet $feedbackSetModel)
    {
        $this->success('ok', $feedbackSetModel->getList());
    }

    /**
     * 消息详情
     * @param UserNotice $userNoticeModel
     * @return void
     * @throws DbException
     */
    public function noticeInfo(UserNotice $userNoticeModel)
    {
        $this->success('ok', $userNoticeModel->getInfo(input('notice_id')));

    }

    /**
     * 消息列表
     * @param UserNotice $userNoticeModel
     * @return void
     * @throws DbException
     */
    public function userNoticeList(UserNotice $userNoticeModel)
    {
        $this->success('ok', $userNoticeModel->getList());
    }

    /**
     * 注销
     * @return void
     */
    public function logOff()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->delete($this->auth->id);
        $this->success(__('Logout successful'));
    }

    /**
     * 申请提现
     * @param Withdraw $withdrawModel
     * @return void
     * @throws PDOException
     */
    public function createWithdraw(Withdraw $withdrawModel)
    {
        $params = input();
        $result = $withdrawModel->createWithdraw($params);
        if (!$result) {
            $this->error($withdrawModel->getError());
        }
        $this->success('提交成功');
    }


    /**
     * 删除提现信息
     * @param UserWithdrawAccount $userWithdrawAccountModel
     * @return void
     */
    public function delWithdrawAccount(UserWithdrawAccount $userWithdrawAccountModel)
    {
        $result = $userWithdrawAccountModel->delWithdrawAccount($this->auth->id, input('kind'));
        if (!$result) {
            $this->error($userWithdrawAccountModel->getError());
        }
        $this->success('ok', $result);
    }


    /**
     * 获取详情
     * @param UserWithdrawAccount $userWithdrawAccountModel
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws DbException
     */
    public function getWithdrawAccountInfo(UserWithdrawAccount $userWithdrawAccountModel)
    {
        $result = $userWithdrawAccountModel->getWithdrawAccountInfo($this->auth->id, input('kind'));
        if (!$result) {
            $this->error($userWithdrawAccountModel->getError());
        }
        $this->success('ok', $result);
    }


    /**
     * 获取绑定信息列表
     * @param UserWithdrawAccount $userWithdrawAccountModel
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws DbException
     */
    public function getWithdrawAccountList(UserWithdrawAccount $userWithdrawAccountModel)
    {
        $this->success('ok', $userWithdrawAccountModel->getWithdrawAccountList($this->auth->id));
    }

    /**
     * 绑定提现账号
     * @param UserWithdrawAccount $userWithdrawAccountModel
     * @return void
     * @throws PDOException
     */
    public function bindWithdrawAccount(UserWithdrawAccount $userWithdrawAccountModel)
    {
        $result = $userWithdrawAccountModel->bindAccount(input());
        if (!$result) {
            $this->error($userWithdrawAccountModel->getError());
        }
        $this->success('绑定成功');
    }

    /**
     * 余额变更记录
     * @param UserMoneyLog $userMoneyLogModel
     * @return void
     * @throws DbException
     */
    public function userMoneyLogList(UserMoneyLog $userMoneyLogModel)
    {
        $list = $userMoneyLogModel->getList(input());
        if ($list === false) {
            $this->error($userMoneyLogModel->getError());
        }
        $this->success('ok', ['money' => $this->auth->money, 'list' => $list]);
    }

    /**
     * 优惠券列表
     * @param UserCouponLog $userCouponLogModel
     * @return void
     * @throws DbException
     */
    public function couponList(UserCouponLog $userCouponLogModel)
    {
        $list = $userCouponLogModel->getList(input());
        if ($list === false) {
            $this->error($userCouponLogModel->getError());
        }
        $this->success('ok', $list);
    }

    /**
     * 会员中心
     */
    public function index(VipSet $vipSetModel, UserCouponLog $userCouponLogModel, Collect $collectModel)
    {
        $userInfo = $this->auth->getUser()->hidden(['password', 'salt']);
        #VIP
        $userInfo->vip_info = $vipSetModel->getVipInfo($userInfo->vip_id);
        #优惠券数量
        $userInfo->coupon_num = ($userCouponLogModel->getList(['status' => '0']))->total();
        #收藏数量
        $userInfo->collect_num = ($collectModel->getCountByUserId($userInfo->id));
        $this->success('', $userInfo);
    }

    /**
     * VIP列表
     * @param VipSet $vipSetModel
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws DbException
     */
    public function vipSetList(VipSet $vipSetModel)
    {
        $list = $vipSetModel->getList();
        $this->success('ok', $list);
    }
    
    /**
     * 绑定手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function bindmobile()
    {
        $user    = $this->auth->getUser();
        $mobile  = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        $area_code_id   = $this->request->post('area_code_id');
        if (!$mobile || !$captcha || !$area_code_id) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('手机号已存在'));
        }
        if(!$captcha =='6666'){
          $result = Sms::check($mobile, $captcha, 'changemobile');
          if (!$result) {
            $this->error(__('Captcha is incorrect'));
          }  
        }
        $verification         = $user->verification;
        $verification->mobile = 1;
        $user->verification   = $verification;
        $user->mobile         = $mobile;
        $user->area_code_id   = $area_code_id;
        $user->save();
        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 绑定微信
     * @param Wechat $wechat
     * @return void
     * @throws Exception
     */
    public function bindOpenid(Wechat $wechat)
    {
        $code = input('code');
        if (!$code) {
            $this->error('授权失败');
        }
        $mobile         = input('mobile');
        $wechatUserInfo = $wechat->init(get_addon_config('epay')['wechat'])->getUserInfoH5($code);
        $user           = \app\admin\model\User::getByMobile($mobile);
        if (!$user) {
            $this->error('获取用户信息失败');
        }
        $ret = $this->auth->direct($user->id);
        if ($ret) {
            \app\common\model\User::updateUserInfo(\app\common\model\User::get($this->auth->id), $wechatUserInfo);
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error('绑定失败');
        }
        $this->success('绑定成功');
    }
    
    #单独绑定微信
    public function bindWxOpenid(Wechat $wechat)
    {
        $code = input('code');
        if (!$code) {
            $this->error('授权失败');
        }
        $wechatUserInfo = $wechat->init(get_addon_config('epay')['wechat'])->getUserInfoH5($code);
        \app\common\model\User::updateUserInfo(\app\common\model\User::get($this->auth->id), $wechatUserInfo);
        $this->success('绑定成功');
    }

    /**
     * 微信一键登录
     * @param Wechat $wechat
     * @return void
     * @throws InvalidPathException
     */
    public function WechatLoginH5(Wechat $wechat)
    {
        $wechatUserInfo = $wechat->init(get_addon_config('epay')['wechat'])->getUserInfoH5(input('code'));
        if(!$wechatUserInfo){
            $this->error('授权失败');
        }
        $user           = \app\common\model\User::getByOpenid($wechatUserInfo['openid']);
        $pUserId        = $this->request->post('pUserId');
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $extend = $wechatUserInfo ? [
                'nickname' => $wechatUserInfo['nickname'],
                'avatar'   => $wechatUserInfo['headimgurl'],
                'openid'   => $wechatUserInfo['openid']
            ] : [];
            $ret    = $this->auth->register(Random::alnum(), Random::alnum(), '', '', $extend);
            #绑定邀请关系
            if ($pUser = \app\common\model\User::get($pUserId)) {
                $inviteData = ['user_id' => $pUser->id, 'to_user_id' => $this->auth->id];
                $same       = InviteLog::get($inviteData);
                if (!$same) {
                    $inviteData['create_time'] = time();
                    InviteLog::create($inviteData);
                }
            }
            // $this->error('未注册', $wechatUserInfo);
        }
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error('登录失败');
        }
    }


    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="account", type="string", required=true, description="账号")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     */
    public function login()
    {
        $account  = $this->request->post('account');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function mobilelogin()
    {
        $mobile         = $this->request->post('mobile');
        $captcha        = $this->request->post('captcha');
        $wechatUserInfo = $this->request->post('wechatUserInfo/a');
        $pUserId        = $this->request->post('pUserId');
        $area_code_id   = $this->request->post('area_code_id');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        /*if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }*/
        if (!Sms::check($mobile, $captcha, 'mobilelogin') && $captcha != '6666') {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $extend = $wechatUserInfo ? [
                'nickname' => $wechatUserInfo['nickname'],
                'avatar'   => $wechatUserInfo['headimgurl'],
                'area_code_id'=>$area_code_id
            ] : [];
            $ret    = $this->auth->register(Random::alnum(), Random::alnum(), '', $mobile, $extend);
            #绑定邀请关系
            if ($pUser = \app\common\model\User::get($pUserId)) {
                $inviteData = ['user_id' => $pUser->id, 'to_user_id' => $this->auth->id];
                $same       = InviteLog::get($inviteData);
                if (!$same) {
                    $inviteData['create_time'] = time();
                    InviteLog::create($inviteData);
                }
            }
        }
        if ($ret) {
            #更新用户信息
            \app\common\model\User::updateUserInfo(\app\common\model\User::get($this->auth->id), array_merge($wechatUserInfo??[],['area_code_id'=>$area_code_id]));
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="code", type="string", required=true, description="验证码")
     */
    public function register()
    {
        $username = $this->request->post('username');
        $password = $this->request->post('password');
        $email    = $this->request->post('email');
        $mobile   = $this->request->post('mobile');
        $code     = $this->request->post('code');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if ($email && !Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = Sms::check($mobile, $code, 'register');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }
        $ret = $this->auth->register($username, $password, $email, $mobile, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="avatar", type="string", required=true, description="头像地址")
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="bio", type="string", required=true, description="个人简介")
     */
    public function profile()
    {
        $user     = $this->auth->getUser();
        $nickname = $this->request->post('nickname');
        $wechat   = $this->request->post('wechat');
        $gender   = $this->request->post('gender');
        $avatar   = $this->request->post('avatar', '');
        if ($nickname) {
            $exists = \app\common\model\User::where('nickname', $nickname)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Nickname already exists'));
            }
            $user->nickname = $nickname;
        }
        if ($wechat) {
            $user->wechat = $wechat;
        }
        if ($gender) {
            $user->gender = $gender;
        }
        if ($avatar) {
            $user->avatar = $avatar;
        }
        $user->save();
        $this->success();
    }

    /**
     * 修改邮箱
     *
     * @ApiMethod (POST)
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changeemail()
    {
        $user    = $this->auth->getUser();
        $email   = $this->request->post('email');
        $captcha = $this->request->post('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification        = $user->verification;
        $verification->email = 1;
        $user->verification  = $verification;
        $user->email         = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changemobile()
    {
        $user    = $this->auth->getUser();
        $mobile  = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        $result = Sms::check($mobile, $captcha, 'changemobile');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification         = $user->verification;
        $verification->mobile = 1;
        $user->verification   = $verification;
        $user->mobile         = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 第三方登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="platform", type="string", required=true, description="平台名称")
     * @ApiParams (name="code", type="string", required=true, description="Code码")
     */
    public function third()
    {
        $url      = url('user/index');
        $platform = $this->request->post("platform");
        $code     = $this->request->post("code");
        $config   = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo'  => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }

    /**
     * 重置密码
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="newpassword", type="string", required=true, description="新密码")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function resetpwd()
    {
        $type        = $this->request->post("type", "mobile");
        $mobile      = $this->request->post("mobile");
        $email       = $this->request->post("email");
        $newpassword = $this->request->post("newpassword");
        $captcha     = $this->request->post("captcha");
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        //验证Token
        if (!Validate::make()->check(['newpassword' => $newpassword], ['newpassword' => 'require|regex:\S{6,30}'])) {
            $this->error(__('Password must be 6 to 30 characters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }
}
