(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-agency-mainPage-invite"],{"00c4":function(i,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var t={props:{lang:String,sessionFrom:String,sendMessageTitle:String,sendMessagePath:String,sendMessageImg:String,showMessageCard:Boolean,appParameter:String,formType:String,openType:String}};n.default=t},"138a":function(i,n,e){"use strict";e.d(n,"b",(function(){return t})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){}));var t=function(){var i=this,n=i.$createElement,e=i._self._c||n;return e("v-uni-view",{staticClass:"u-icon",class:["u-icon--"+i.labelPos],on:{click:function(n){arguments[0]=n=i.$handleEvent(n),i.clickHandler.apply(void 0,arguments)}}},[i.isImg?e("v-uni-image",{staticClass:"u-icon__img",style:[i.imgStyle,i.$u.addStyle(i.customStyle)],attrs:{src:i.name,mode:i.imgMode}}):e("v-uni-text",{staticClass:"u-icon__icon",class:i.uClasses,style:[i.iconStyle,i.$u.addStyle(i.customStyle)],attrs:{"hover-class":i.hoverClass}},[i._v(i._s(i.icon))]),""!==i.label?e("v-uni-text",{staticClass:"u-icon__label",style:{color:i.labelColor,fontSize:i.$u.addUnit(i.labelSize),marginLeft:"right"==i.labelPos?i.$u.addUnit(i.space):0,marginTop:"bottom"==i.labelPos?i.$u.addUnit(i.space):0,marginRight:"left"==i.labelPos?i.$u.addUnit(i.space):0,marginBottom:"top"==i.labelPos?i.$u.addUnit(i.space):0}},[i._v(i._s(i.label))]):i._e()],1)},o=[]},2002:function(i,n,e){"use strict";e.d(n,"b",(function(){return t})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){}));var t=function(){var i=this.$createElement,n=this._self._c||i;return n("v-uni-view",{staticClass:"u-gap",style:[this.gapStyle]})},o=[]},"22b4":function(i,n,e){"use strict";e.r(n);var t=e("5fb8"),o=e.n(t);for(var u in t)["default"].indexOf(u)<0&&function(i){e.d(n,i,(function(){return t[i]}))}(u);n["default"]=o.a},"2ec5":function(i,n,e){"use strict";i.exports=function(i,n){return n||(n={}),i=i&&i.__esModule?i.default:i,"string"!==typeof i?i:(/^['"].*['"]$/.test(i)&&(i=i.slice(1,-1)),n.hash&&(i+=n.hash),/["'() \t\n]/.test(i)||n.needQuotes?'"'.concat(i.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):i)}},"30f7":function(i,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e("7a76"),e("c9b5")},"35a9":function(i,n,e){i.exports=e.p+"static/user/kefuBack.png"},4733:function(i,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(i){if(Array.isArray(i))return(0,t.default)(i)};var t=function(i){return i&&i.__esModule?i:{default:i}}(e("8d0b"))},5017:function(i,n,e){"use strict";e.r(n);var t=e("8ca3"),o=e("7f35");for(var u in o)["default"].indexOf(u)<0&&function(i){e.d(n,i,(function(){return o[i]}))}(u);e("b50d");var c=e("828b"),a=Object(c["a"])(o["default"],t["b"],t["c"],!1,null,"2daf1906",null,!1,t["a"],void 0);n["default"]=a.exports},"50ee":function(i,n,e){var t=e("c86c");n=t(!1),n.push([i.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */uni-view[data-v-59765974], uni-scroll-view[data-v-59765974], uni-swiper-item[data-v-59765974]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}@font-face{font-family:uicon-iconfont;src:url(https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf) format("truetype")}.u-icon[data-v-59765974]{display:flex;align-items:center}.u-icon--left[data-v-59765974]{flex-direction:row-reverse;align-items:center}.u-icon--right[data-v-59765974]{flex-direction:row;align-items:center}.u-icon--top[data-v-59765974]{flex-direction:column-reverse;justify-content:center}.u-icon--bottom[data-v-59765974]{flex-direction:column;justify-content:center}.u-icon__icon[data-v-59765974]{font-family:uicon-iconfont;position:relative;\ndisplay:flex;\nflex-direction:row;align-items:center}.u-icon__icon--primary[data-v-59765974]{color:#3c9cff}.u-icon__icon--success[data-v-59765974]{color:#5ac725}.u-icon__icon--error[data-v-59765974]{color:#f56c6c}.u-icon__icon--warning[data-v-59765974]{color:#f9ae3d}.u-icon__icon--info[data-v-59765974]{color:#909399}.u-icon__img[data-v-59765974]{height:auto;will-change:transform}.u-icon__label[data-v-59765974]{line-height:1}',""]),i.exports=n},"5c9f":function(i,n,e){var t=e("c86c");n=t(!1),n.push([i.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */uni-view[data-v-3927d88e], uni-scroll-view[data-v-3927d88e], uni-swiper-item[data-v-3927d88e]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}',""]),i.exports=n},"5fb8":function(i,n,e){"use strict";e("6a54");var t=e("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("aa9c"),e("4626"),e("5ac7"),e("5ef2");var o=t(e("9d4d")),u=t(e("e73e")),c={name:"u-icon",data:function(){return{}},mixins:[uni.$u.mpMixin,uni.$u.mixin,u.default],computed:{uClasses:function(){var i=[];return i.push(this.customPrefix+"-"+this.name),this.color&&uni.$u.config.type.includes(this.color)&&i.push("u-icon__icon--"+this.color),i},iconStyle:function(){var i={};return i={fontSize:uni.$u.addUnit(this.size),lineHeight:uni.$u.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:uni.$u.addUnit(this.top)},this.color&&!uni.$u.config.type.includes(this.color)&&(i.color=this.color),i},isImg:function(){return-1!==this.name.indexOf("/")},imgStyle:function(){var i={};return i.width=this.width?uni.$u.addUnit(this.width):uni.$u.addUnit(this.size),i.height=this.height?uni.$u.addUnit(this.height):uni.$u.addUnit(this.size),i},icon:function(){return o.default["uicon-"+this.name]||this.name}},methods:{clickHandler:function(i){this.$emit("click",this.index),this.stop&&this.preventEvent(i)}}};n.default=c},"7eee":function(i,n,e){"use strict";var t=e("e52d"),o=e.n(t);o.a},"7f35":function(i,n,e){"use strict";e.r(n);var t=e("e68f"),o=e.n(t);for(var u in t)["default"].indexOf(u)<0&&function(i){e.d(n,i,(function(){return t[i]}))}(u);n["default"]=o.a},"84db":function(i,n,e){"use strict";e.r(n);var t=e("138a"),o=e("22b4");for(var u in o)["default"].indexOf(u)<0&&function(i){e.d(n,i,(function(){return o[i]}))}(u);e("7eee");var c=e("828b"),a=Object(c["a"])(o["default"],t["b"],t["c"],!1,null,"59765974",null,!1,t["a"],void 0);n["default"]=a.exports},"8ca3":function(i,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){return t}));var t={uGap:e("9ab5").default,uAvatar:e("1204").default},o=function(){var i=this,n=i.$createElement,e=i._self._c||n;return e("v-uni-view",{},[e("v-uni-view",{staticClass:"back"},[e("u-gap",{attrs:{height:"10"}}),e("v-uni-view",{staticClass:"topBox"},[e("v-uni-view",{staticClass:"size-50 bold"},[i._v("邀请用户 消费得返佣")])],1),e("v-uni-view",{staticClass:"text-center w-400",staticStyle:{margin:"20rpx auto"}},[e("v-uni-image",{staticClass:"w-400 h-400",attrs:{src:i.$getimgsrc(i.ercodeUrl),mode:""}})],1),e("v-uni-view",{staticClass:"w-280 center bold",staticStyle:{margin:"20rpx auto"}},[i._v("长按保存二维码")])],1),e("v-uni-view",{staticClass:"flex flex-middle w-690",staticStyle:{margin:"30rpx auto"}},[e("v-uni-view",{staticClass:"size-30 flex1 flex-middle"},[e("v-uni-view",{staticStyle:{"background-color":"#07C160",width:"6rpx",height:"28rpx","margin-right":"10rpx"}}),i._v("邀请记录")],1),e("v-uni-view",{staticClass:"size-26"},[i._v("累计邀请 "+i._s(i.total)+" 人")])],1),i._l(i.list,(function(n,t){return e("v-uni-view",{key:t,staticClass:"pd-20 flex1 flex-middle u-border-bottom"},[e("u-avatar",{attrs:{src:i.$getimgsrc(n.avatar),size:"44"}}),e("v-uni-view",{staticClass:"ml-12",staticStyle:{flex:"2"}},[e("v-uni-view",{staticClass:"size-30"},[i._v(i._s(n.nickname))]),e("v-uni-view",{staticClass:"flex"},[e("v-uni-view",{staticClass:"size-24 mt-10",staticStyle:{color:"#999999"}},[i._v("注册时间："+i._s(n.create_time_text))])],1)],1)],1)}))],2)},u=[]},"8dc9":function(i,n,e){"use strict";e("6a54");var t=e("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=t(e("bf8d")),u={name:"u-gap",mixins:[uni.$u.mpMixin,uni.$u.mixin,o.default],computed:{gapStyle:function(){var i={backgroundColor:this.bgColor,height:uni.$u.addUnit(this.height),marginTop:uni.$u.addUnit(this.marginTop),marginBottom:uni.$u.addUnit(this.marginBottom)};return uni.$u.deepMerge(i,uni.$u.addStyle(this.customStyle))}}};n.default=u},"9ab5":function(i,n,e){"use strict";e.r(n);var t=e("2002"),o=e("be8b");for(var u in o)["default"].indexOf(u)<0&&function(i){e.d(n,i,(function(){return o[i]}))}(u);e("ba46");var c=e("828b"),a=Object(c["a"])(o["default"],t["b"],t["c"],!1,null,"3927d88e",null,!1,t["a"],void 0);n["default"]=a.exports},"9d4d":function(i,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={"uicon-level":"","uicon-column-line":"","uicon-checkbox-mark":"","uicon-folder":"","uicon-movie":"","uicon-star-fill":"","uicon-star":"","uicon-phone-fill":"","uicon-phone":"","uicon-apple-fill":"","uicon-chrome-circle-fill":"","uicon-backspace":"","uicon-attach":"","uicon-cut":"","uicon-empty-car":"","uicon-empty-coupon":"","uicon-empty-address":"","uicon-empty-favor":"","uicon-empty-permission":"","uicon-empty-news":"","uicon-empty-search":"","uicon-github-circle-fill":"","uicon-rmb":"","uicon-person-delete-fill":"","uicon-reload":"","uicon-order":"","uicon-server-man":"","uicon-search":"","uicon-fingerprint":"","uicon-more-dot-fill":"","uicon-scan":"","uicon-share-square":"","uicon-map":"","uicon-map-fill":"","uicon-tags":"","uicon-tags-fill":"","uicon-bookmark-fill":"","uicon-bookmark":"","uicon-eye":"","uicon-eye-fill":"","uicon-mic":"","uicon-mic-off":"","uicon-calendar":"","uicon-calendar-fill":"","uicon-trash":"","uicon-trash-fill":"","uicon-play-left":"","uicon-play-right":"","uicon-minus":"","uicon-plus":"","uicon-info":"","uicon-info-circle":"","uicon-info-circle-fill":"","uicon-question":"","uicon-error":"","uicon-close":"","uicon-checkmark":"","uicon-android-circle-fill":"","uicon-android-fill":"","uicon-ie":"","uicon-IE-circle-fill":"","uicon-google":"","uicon-google-circle-fill":"","uicon-setting-fill":"","uicon-setting":"","uicon-minus-square-fill":"","uicon-plus-square-fill":"","uicon-heart":"","uicon-heart-fill":"","uicon-camera":"","uicon-camera-fill":"","uicon-more-circle":"","uicon-more-circle-fill":"","uicon-chat":"","uicon-chat-fill":"","uicon-bag-fill":"","uicon-bag":"","uicon-error-circle-fill":"","uicon-error-circle":"","uicon-close-circle":"","uicon-close-circle-fill":"","uicon-checkmark-circle":"","uicon-checkmark-circle-fill":"","uicon-question-circle-fill":"","uicon-question-circle":"","uicon-share":"","uicon-share-fill":"","uicon-shopping-cart":"","uicon-shopping-cart-fill":"","uicon-bell":"","uicon-bell-fill":"","uicon-list":"","uicon-list-dot":"","uicon-zhihu":"","uicon-zhihu-circle-fill":"","uicon-zhifubao":"","uicon-zhifubao-circle-fill":"","uicon-weixin-circle-fill":"","uicon-weixin-fill":"","uicon-twitter-circle-fill":"","uicon-twitter":"","uicon-taobao-circle-fill":"","uicon-taobao":"","uicon-weibo-circle-fill":"","uicon-weibo":"","uicon-qq-fill":"","uicon-qq-circle-fill":"","uicon-moments-circel-fill":"","uicon-moments":"","uicon-qzone":"","uicon-qzone-circle-fill":"","uicon-baidu-circle-fill":"","uicon-baidu":"","uicon-facebook-circle-fill":"","uicon-facebook":"","uicon-car":"","uicon-car-fill":"","uicon-warning-fill":"","uicon-warning":"","uicon-clock-fill":"","uicon-clock":"","uicon-edit-pen":"","uicon-edit-pen-fill":"","uicon-email":"","uicon-email-fill":"","uicon-minus-circle":"","uicon-minus-circle-fill":"","uicon-plus-circle":"","uicon-plus-circle-fill":"","uicon-file-text":"","uicon-file-text-fill":"","uicon-pushpin":"","uicon-pushpin-fill":"","uicon-grid":"","uicon-grid-fill":"","uicon-play-circle":"","uicon-play-circle-fill":"","uicon-pause-circle-fill":"","uicon-pause":"","uicon-pause-circle":"","uicon-eye-off":"","uicon-eye-off-outline":"","uicon-gift-fill":"","uicon-gift":"","uicon-rmb-circle-fill":"","uicon-rmb-circle":"","uicon-kefu-ermai":"","uicon-server-fill":"","uicon-coupon-fill":"","uicon-coupon":"","uicon-integral":"","uicon-integral-fill":"","uicon-home-fill":"","uicon-home":"","uicon-hourglass-half-fill":"","uicon-hourglass":"","uicon-account":"","uicon-plus-people-fill":"","uicon-minus-people-fill":"","uicon-account-fill":"","uicon-thumb-down-fill":"","uicon-thumb-down":"","uicon-thumb-up":"","uicon-thumb-up-fill":"","uicon-lock-fill":"","uicon-lock-open":"","uicon-lock-opened-fill":"","uicon-lock":"","uicon-red-packet-fill":"","uicon-photo-fill":"","uicon-photo":"","uicon-volume-off-fill":"","uicon-volume-off":"","uicon-volume-fill":"","uicon-volume":"","uicon-red-packet":"","uicon-download":"","uicon-arrow-up-fill":"","uicon-arrow-down-fill":"","uicon-play-left-fill":"","uicon-play-right-fill":"","uicon-rewind-left-fill":"","uicon-rewind-right-fill":"","uicon-arrow-downward":"","uicon-arrow-leftward":"","uicon-arrow-rightward":"","uicon-arrow-upward":"","uicon-arrow-down":"","uicon-arrow-right":"","uicon-arrow-left":"","uicon-arrow-up":"","uicon-skip-back-left":"","uicon-skip-forward-right":"","uicon-rewind-right":"","uicon-rewind-left":"","uicon-arrow-right-double":"","uicon-arrow-left-double":"","uicon-wifi-off":"","uicon-wifi":"","uicon-empty-data":"","uicon-empty-history":"","uicon-empty-list":"","uicon-empty-page":"","uicon-empty-order":"","uicon-man":"","uicon-woman":"","uicon-man-add":"","uicon-man-add-fill":"","uicon-man-delete":"","uicon-man-delete-fill":"","uicon-zh":"","uicon-en":""}},b4ff:function(i,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var t={props:{openType:String},methods:{onGetUserInfo:function(i){this.$emit("getuserinfo",i.detail)},onContact:function(i){this.$emit("contact",i.detail)},onGetPhoneNumber:function(i){this.$emit("getphonenumber",i.detail)},onError:function(i){this.$emit("error",i.detail)},onLaunchApp:function(i){this.$emit("launchapp",i.detail)},onOpenSetting:function(i){this.$emit("opensetting",i.detail)}}};n.default=t},b50d:function(i,n,e){"use strict";var t=e("eac2"),o=e.n(t);o.a},b7c7:function(i,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(i){return(0,t.default)(i)||(0,o.default)(i)||(0,u.default)(i)||(0,c.default)()};var t=a(e("4733")),o=a(e("d14d")),u=a(e("5d6b")),c=a(e("30f7"));function a(i){return i&&i.__esModule?i:{default:i}}},ba46:function(i,n,e){"use strict";var t=e("e8fe"),o=e.n(t);o.a},be8b:function(i,n,e){"use strict";e.r(n);var t=e("8dc9"),o=e.n(t);for(var u in t)["default"].indexOf(u)<0&&function(i){e.d(n,i,(function(){return t[i]}))}(u);n["default"]=o.a},bf8d:function(i,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("64aa");var t={props:{bgColor:{type:String,default:uni.$u.props.gap.bgColor},height:{type:[String,Number],default:uni.$u.props.gap.height},marginTop:{type:[String,Number],default:uni.$u.props.gap.marginTop},marginBottom:{type:[String,Number],default:uni.$u.props.gap.marginBottom}}};n.default=t},e397:function(i,n,e){var t=e("c86c"),o=e("2ec5"),u=e("35a9");n=t(!1);var c=o(u);n.push([i.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.back[data-v-2daf1906]{background-image:url('+c+");width:%?750?%;background-size:100% %?880?%;padding-bottom:%?30?%}.topBox[data-v-2daf1906]{width:%?550?%;height:%?100?%;border-radius:%?64?% %?64?% %?64?% %?64?%;margin:auto;text-align:center;line-height:%?100?%}",""]),i.exports=n},e52d:function(i,n,e){var t=e("50ee");t.__esModule&&(t=t.default),"string"===typeof t&&(t=[[i.i,t,""]]),t.locals&&(i.exports=t.locals);var o=e("967d").default;o("7af9b780",t,!0,{sourceMap:!1,shadowMode:!1})},e68f:function(i,n,e){"use strict";e("6a54");var t=e("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("c223");var o=t(e("b7c7")),u=e("b4ef"),c={data:function(){return{page:1,list:[],total:0,ercodeUrl:""}},onLoad:function(){this.getList(),this.getUrl()},onReachBottom:function(){this.list.length<15*this.page||(this.page++,this.getList())},methods:{getUrl:function(){var i=this;(0,u.DlgetInviteUrl)().then((function(n){i.ercodeUrl=n.data}))},getList:function(){var i=this;(0,u.getInviteList)({page:this.page}).then((function(n){console.log(n),i.list=[].concat((0,o.default)(i.list),(0,o.default)(n.data.data)),i.total=n.data.total}))}}};n.default=c},e73e:function(i,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("64aa");var t={props:{name:{type:String,default:uni.$u.props.icon.name},color:{type:String,default:uni.$u.props.icon.color},size:{type:[String,Number],default:uni.$u.props.icon.size},bold:{type:Boolean,default:uni.$u.props.icon.bold},index:{type:[String,Number],default:uni.$u.props.icon.index},hoverClass:{type:String,default:uni.$u.props.icon.hoverClass},customPrefix:{type:String,default:uni.$u.props.icon.customPrefix},label:{type:[String,Number],default:uni.$u.props.icon.label},labelPos:{type:String,default:uni.$u.props.icon.labelPos},labelSize:{type:[String,Number],default:uni.$u.props.icon.labelSize},labelColor:{type:String,default:uni.$u.props.icon.labelColor},space:{type:[String,Number],default:uni.$u.props.icon.space},imgMode:{type:String,default:uni.$u.props.icon.imgMode},width:{type:[String,Number],default:uni.$u.props.icon.width},height:{type:[String,Number],default:uni.$u.props.icon.height},top:{type:[String,Number],default:uni.$u.props.icon.top},stop:{type:Boolean,default:uni.$u.props.icon.stop}}};n.default=t},e8fe:function(i,n,e){var t=e("5c9f");t.__esModule&&(t=t.default),"string"===typeof t&&(t=[[i.i,t,""]]),t.locals&&(i.exports=t.locals);var o=e("967d").default;o("038de04b",t,!0,{sourceMap:!1,shadowMode:!1})},eac2:function(i,n,e){var t=e("e397");t.__esModule&&(t=t.default),"string"===typeof t&&(t=[[i.i,t,""]]),t.locals&&(i.exports=t.locals);var o=e("967d").default;o("48d39b71",t,!0,{sourceMap:!1,shadowMode:!1})}}]);