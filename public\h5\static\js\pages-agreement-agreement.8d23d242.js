(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-agreement-agreement"],{"498e":function(t,n,e){"use strict";e.d(n,"b",(function(){return r})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){return i}));var i={uParse:e("ca8d").default},r=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",[n("u-parse",{attrs:{content:this.content,domain:this.$Url}})],1)},u=[]},"61c5":function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=e("c2f9"),r={data:function(){return{id:"",content:""}},onLoad:function(t){this.id=t.id,this.getinfo()},methods:{getinfo:function(){var t=this;(0,i.getAgreement)({agreement_id:this.id}).then((function(n){t.content=n.data.content}))}}};n.default=r},d86b:function(t,n,e){"use strict";e.r(n);var i=e("61c5"),r=e.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(u);n["default"]=r.a},ec9c:function(t,n,e){"use strict";e.r(n);var i=e("498e"),r=e("d86b");for(var u in r)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return r[t]}))}(u);var a=e("828b"),c=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"56047a68",null,!1,i["a"],void 0);n["default"]=c.exports}}]);