(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-agreement-agreement"],{"59a7":function(t,n,e){"use strict";e.r(n);var i=e("5a0d"),a=e.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(r);n["default"]=a.a},"5a0d":function(t,n,e){"use strict";e("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=e("b4ef"),a={data:function(){return{id:"",content:""}},onLoad:function(t){this.id=t.id,this.getinfo()},methods:{getinfo:function(){var t=this;(0,i.getAgreement)({agreement_id:this.id}).then((function(n){t.content=n.data.content}))}}};n.default=a},c17d:function(t,n,e){"use strict";e.r(n);var i=e("f4e8"),a=e("59a7");for(var r in a)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(r);var u=e("828b"),o=Object(u["a"])(a["default"],i["b"],i["c"],!1,null,"56047a68",null,!1,i["a"],void 0);n["default"]=o.exports},f4e8:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return r})),e.d(n,"a",(function(){return i}));var i={uParse:e("65a2").default},a=function(){var t=this.$createElement,n=this._self._c||t;return n("v-uni-view",[n("u-parse",{attrs:{content:this.content,domain:this.$Url}})],1)},r=[]}}]);